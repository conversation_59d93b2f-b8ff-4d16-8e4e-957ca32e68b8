// Copyright (C) 2017 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
apply from: '../../constants.gradle'
apply plugin: 'com.android.application'

android {
    namespace 'androidx.media3.demo.cast'

    compileSdk project.ext.compileSdkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        versionName project.ext.releaseVersion
        versionCode project.ext.releaseVersionCode
        minSdkVersion project.ext.minSdkVersion
        targetSdkVersion project.ext.appTargetSdkVersion
    }

    buildTypes {
        release {
            shrinkResources true
            minifyEnabled true
            proguardFiles = [
                "proguard-rules.txt",
                getDefaultProguardFile('proguard-android.txt')
            ]
            signingConfig signingConfigs.debug
        }
        debug {
            jniDebuggable = true
        }
    }

    lintOptions {
        // The demo app isn't indexed and doesn't have translations.
        disable 'GoogleAppIndexingWarning','MissingTranslation'
    }
}

dependencies {
    implementation project(modulePrefix + 'lib-exoplayer')
    implementation project(modulePrefix + 'lib-exoplayer-dash')
    implementation project(modulePrefix + 'lib-exoplayer-hls')
    implementation project(modulePrefix + 'lib-exoplayer-rtsp')
    implementation project(modulePrefix + 'lib-exoplayer-smoothstreaming')
    implementation project(modulePrefix + 'lib-ui')
    implementation project(modulePrefix + 'lib-cast')
    implementation 'androidx.appcompat:appcompat:' + androidxAppCompatVersion
    implementation 'androidx.recyclerview:recyclerview:' + androidxRecyclerViewVersion
    implementation 'com.google.android.material:material:' + androidxMaterialVersion
}

apply plugin: 'com.google.android.gms.strict-version-matcher-plugin'
