<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- ANDROID COMPONENT-SPECIFIC STYLES.-->

    <!--
    Hacks around the incorrectly-sized navigation icon in the Toolbar, changing it from
    56dp to 48dp.
    -->
    <style name="Widget.Auxio.Toolbar.Navigation" parent="Widget.AppCompat.Toolbar.Button.Navigation">
        <item name="android:minWidth">@dimen/size_touchable_small</item>
    </style>

    <!--
    Hacks around the old overflow button that was deliberately downsized to 36dp
    (presumably for compat with older devices)
    -->
    <style name="Widget.Auxio.Button.Overflow" parent="Widget.AppCompat.ActionButton.Overflow">
        <item name="android:minWidth">@dimen/size_touchable_small</item>
        <item name="android:minHeight">@dimen/size_touchable_small</item>
        <item name="android:paddingStart">0dp</item>
        <item name="android:paddingEnd">0dp</item>
    </style>

    <style name="Theme.Auxio.Dialog" parent="ThemeOverlay.Material3.MaterialAlertDialog">
        <item name="android:checkedTextViewStyle">@style/Widget.Auxio.Dialog.CheckedTextView</item>
    </style>

    <!-- The style for the checked text view in the custom dialog -->
    <style name="Widget.Auxio.Dialog.CheckedTextView" parent="Widget.Material3.CheckedTextView">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <!-- Dialog style that properly implements dividers in a NestedScrollView -->
    <style name="Widget.Auxio.Dialog.NestedScrollView" parent="">
        <item name="android:overScrollMode">never</item>
        <item name="android:paddingTop">@dimen/spacing_medium</item>

        <!-- API 23+ specific -->
        <item name="android:scrollIndicators" tools:ignore="NewApi">top|bottom</item>
    </style>

    <style name="PreferenceTheme.Auxio" parent="@style/PreferenceThemeOverlay">
        <item name="android:layout">@layout/fragment_preferences</item>
    </style>

    <style name="Preference.Auxio" parent="Preference.Material">
        <item name="allowDividerBelow">false</item>
        <item name="iconSpaceReserved">false</item>
        <item name="android:background">@android:color/black</item>
    </style>

    <style name="Preference.Auxio.DialogPreference" parent="Preference.DialogPreference.Material">
        <item name="allowDividerBelow">false</item>
        <item name="iconSpaceReserved">false</item>
    </style>

    <style name="Preference.Auxio.PreferenceCategory" parent="Preference.Category.Material">
        <item name="layout">@layout/item_header</item>
        <item name="allowDividerBelow">false</item>
        <item name="iconSpaceReserved">false</item>
    </style>

    <style name="Preference.Auxio.SwitchPreferenceCompat" parent="Preference.SwitchPreferenceCompat.Material">
        <item name="widgetLayout">@layout/view_preference_switch</item>
        <item name="allowDividerBelow">false</item>
        <item name="iconSpaceReserved">false</item>
    </style>

    <!-- Widget TextView that mimics the main Auxio Primary/Secondary TextViews. -->
    <style name="Widget.Auxio.TextView.AppWidget" parent="Widget.Auxio.TextView.Base">
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
    </style>

    <!-- Widget TextView that mimics the main Auxio Primary TextView -->
    <style name="Widget.Auxio.TextView.Primary.AppWidget" parent="Widget.Auxio.TextView.AppWidget">
        <item name="android:textAppearance">@style/TextAppearance.Material3.TitleMedium</item>
    </style>

    <!-- Widget TextView that mimics the main Auxio Secondary TextView -->
    <style name="Widget.Auxio.TextView.Secondary.AppWidget" parent="Widget.Auxio.TextView.AppWidget">
        <item name="android:textColor">?android:attr/textColorSecondary</item>
        <item name="android:textAppearance">@style/TextAppearance.Material3.BodyMedium</item>
    </style>

    <!-- A variant of PlaybackButton that plays along with AppWidget restrictions. -->
    <style name="Widget.Auxio.MaterialButton.AppWidget" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:minHeight">@dimen/size_touchable_small</item>
        <item name="android:background">?android:attr/selectableItemBackgroundBorderless</item>
    </style>

    <!-- A variant of button that emulates a FAB-ish button that plays along with widget restrictions -->
    <style name="Widget.Auxio.MaterialButton.AppWidget.PlayPause" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:minWidth">@dimen/size_touchable_small</item>
        <item name="android:background">@drawable/ui_remote_fab_container_paused</item>
        <item name="android:tint">?attr/colorOnPrimary</item>
    </style>
</resources>