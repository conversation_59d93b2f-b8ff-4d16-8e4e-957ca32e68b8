<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<resources>

  <string name="application_name">Exo Cast Demo</string>

  <string name="media_route_menu_title">Cast</string>

  <string name="add_samples">Add samples</string>

  <string name="cast_context_error">Failed to get Cast context. Try updating Google Play Services and restart the app.</string>

  <string name="error_unsupported_video">Media includes video tracks, but none are playable by this device</string>

  <string name="error_unsupported_audio">Media includes audio tracks, but none are playable by this device</string>

</resources>
