<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2023 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="androidx.media3.demo.shortform">

  <application
    android:allowBackup="false"
    android:icon="@mipmap/ic_launcher"
    android:label="@string/app_name"
    android:theme="@style/Theme.MaterialComponents.DayNight.NoActionBar"
    android:taskAffinity="">
    <activity
      android:exported="true"
      android:name=".MainActivity">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>
    <activity
      android:exported="false"
      android:label="@string/title_activity_view_pager"
      android:name=".viewpager.ViewPagerActivity"/>
  </application>

  <uses-permission android:name="android.permission.INTERNET" />

  <uses-sdk />

</manifest>
