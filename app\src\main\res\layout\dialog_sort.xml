<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!--
    Required to use a LinearLayout here for space allocation to stop the BottomSheetDialog
    from flipping out and not allowing the RecyclerView to scroll fully.
    -->

    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/menu_handle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/sort_name"
        style="@style/Widget.Auxio.TextView.Primary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/spacing_medium"
        android:text="@string/lbl_sort_mode"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <org.oxycblt.auxio.list.recycler.DialogRecyclerView
        android:id="@+id/sort_mode_recycler"
        style="@style/Widget.Auxio.RecyclerView.Linear"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        tools:listitem="@layout/item_sort_mode" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/sort_header"
            style="@style/Widget.Auxio.TextView.Header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/lbl_sort_direction"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.button.MaterialButtonToggleGroup
            android:id="@+id/sort_direction_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/spacing_medium"
            android:layout_marginTop="@dimen/spacing_tiny"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/sort_header"
            app:selectionRequired="false"
            app:singleSelection="true"
            tools:layout_editor_absoluteX="24dp">

            <org.oxycblt.auxio.ui.RippleFixMaterialButton
                android:id="@+id/sort_direction_asc"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/lbl_sort_asc"
                tools:icon="@drawable/ic_check_24" />

            <org.oxycblt.auxio.ui.RippleFixMaterialButton
                android:id="@+id/sort_direction_dsc"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/lbl_sort_dsc" />

        </com.google.android.material.button.MaterialButtonToggleGroup>

        <org.oxycblt.auxio.ui.RippleFixMaterialButton
            android:id="@+id/sort_cancel"
            style="@style/Widget.Material3.Button.TextButton.Dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/spacing_small"
            android:text="@string/lbl_cancel"
            app:layout_constraintBottom_toBottomOf="@+id/sort_save"
            app:layout_constraintEnd_toStartOf="@+id/sort_save"
            app:layout_constraintTop_toTopOf="@+id/sort_save" />

        <org.oxycblt.auxio.ui.RippleFixMaterialButton
            android:id="@+id/sort_save"
            style="@style/Widget.Material3.Button.TextButton.Dialog"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/spacing_mid_medium"
            android:layout_marginEnd="@dimen/spacing_medium"
            android:layout_marginBottom="@dimen/spacing_mid_medium"
            android:text="@string/lbl_ok"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/sort_direction_group" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>