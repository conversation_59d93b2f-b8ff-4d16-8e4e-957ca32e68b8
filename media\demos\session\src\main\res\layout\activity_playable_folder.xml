<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent">

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:id="@+id/linear_layout"
    tools:context=".MainActivity">
    <TextView
      android:id="@+id/folder_description"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:text="@string/current_playlist_name"
      android:textAppearance="@style/TextAppearance.AppCompat.Display3" />


    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:gravity="center_horizontal"
      android:orientation="horizontal">

      <Button
        android:id="@+id/shuffle_button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_margin="10dp"
        android:textColor="@color/white"
        android:drawableLeft="@drawable/exo_styled_controls_shuffle_on"
        android:text="@string/shuffle" />

      <Button
        android:id="@+id/play_button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_margin="10dp"
        android:drawableLeft="@drawable/exo_icon_play"
        android:text="@string/play_button" />

    </LinearLayout>

    <ListView
      android:id="@+id/media_list_view"
      android:layout_width="match_parent"
      android:layout_height="match_parent">
    </ListView>

  </LinearLayout>
  <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
    android:id="@+id/open_player_floating_button"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom|center_horizontal"
    android:textColor="@color/white"
    android:layout_margin="10dp"
    app:icon="@drawable/exo_icon_play"
    app:iconTint="@color/white"
    app:backgroundTint="@color/purple_500"
    android:text="@string/current_playlist_name"
    android:contentDescription="@string/open_player_content_description" />
</FrameLayout>
