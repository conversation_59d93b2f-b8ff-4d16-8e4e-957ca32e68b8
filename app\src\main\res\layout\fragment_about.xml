<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/colorSurface"
    android:orientation="vertical"
    android:transitionGroup="true"
    tools:context=".settings.AboutFragment">

    <org.oxycblt.auxio.ui.CoordinatorAppBarLayout
        android:id="@+id/about_appbar"
        style="@style/Widget.Auxio.AppBarLayout"
        app:liftOnScroll="true">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/about_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:navigationIcon="@drawable/ic_back_24"
            app:title="@string/lbl_about" />

    </org.oxycblt.auxio.ui.CoordinatorAppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/about_contents"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/spacing_medium"
            android:orientation="vertical">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingBottom="@dimen/spacing_tiny">

                    <ImageView
                        android:id="@+id/about_auxio_icon"
                        style="@style/Widget.Auxio.Image.Small"
                        android:layout_marginTop="@dimen/spacing_medium"
                        android:contentDescription="@string/desc_auxio_icon"
                        android:src="@mipmap/ic_launcher"
                        app:layout_constraintEnd_toStartOf="@+id/about_app_name"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/about_app_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/spacing_medium"
                        android:text="@string/info_app_name"
                        android:textAppearance="@style/TextAppearance.Auxio.TitleLarge"
                        app:layout_constraintBottom_toBottomOf="@+id/about_auxio_icon"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/about_auxio_icon"
                        app:layout_constraintTop_toTopOf="@+id/about_auxio_icon" />

                    <TextView
                        android:id="@+id/about_desc"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/spacing_small"
                        android:gravity="center"
                        android:paddingStart="@dimen/spacing_medium"
                        android:paddingEnd="@dimen/spacing_medium"
                        android:text="@string/info_app_desc"
                        android:textAppearance="@style/TextAppearance.Auxio.BodyLarge"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_auxio_icon" />


                    <ImageView
                        android:id="@+id/about_version_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/spacing_medium"
                        android:contentDescription="@string/lbl_version"
                        android:src="@drawable/ic_about_24"
                        app:layout_constraintBottom_toBottomOf="@+id/about_version"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/about_version_title" />

                    <TextView
                        android:id="@+id/about_version_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/spacing_medium"
                        android:layout_marginTop="@dimen/spacing_mid_medium"
                        android:text="@string/lbl_version"
                        android:textAppearance="@style/TextAppearance.Auxio.BodyLarge"
                        app:layout_constraintBottom_toTopOf="@+id/about_version"
                        app:layout_constraintStart_toEndOf="@+id/about_version_icon"
                        app:layout_constraintTop_toBottomOf="@+id/about_desc" />

                    <TextView
                        android:id="@+id/about_version"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/spacing_medium"
                        android:textAppearance="@style/TextAppearance.Auxio.BodySmall"
                        app:layout_constraintStart_toStartOf="@+id/about_version_title"
                        app:layout_constraintTop_toBottomOf="@+id/about_version_title"
                        tools:text="16.16.16" />

                    <TextView
                        android:id="@+id/about_code"
                        style="@style/Widget.Auxio.TextView.Icon.Clickable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/spacing_mid_medium"
                        android:text="@string/lbl_code"
                        app:drawableStartCompat="@drawable/ic_code_24"
                        app:layout_constraintBottom_toTopOf="@+id/about_wiki"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_version" />

                    <TextView
                        android:id="@+id/about_wiki"
                        style="@style/Widget.Auxio.TextView.Icon.Clickable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/lbl_wiki"
                        app:drawableStartCompat="@drawable/ic_help_24"
                        app:layout_constraintBottom_toTopOf="@+id/about_licenses"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_code" />

                    <TextView
                        android:id="@+id/about_licenses"
                        style="@style/Widget.Auxio.TextView.Icon.Clickable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/lbl_licenses"
                        app:drawableStartCompat="@drawable/ic_license_24"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_wiki" />


                </androidx.constraintlayout.widget.ConstraintLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/spacing_medium">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/spacing_tiny">

                    <TextView
                        android:id="@+id/about_author"
                        style="@style/Widget.Auxio.TextView.Header"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/lbl_author"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <TextView
                        android:id="@+id/about_profile"
                        style="@style/Widget.Auxio.TextView.Icon.Clickable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/lbl_author_name"
                        app:drawableStartCompat="@drawable/ic_person_24"
                        app:drawableTint="?attr/colorControlNormal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_licenses" />

                    <TextView
                        android:id="@+id/about_donate"
                        style="@style/Widget.Auxio.TextView.Icon.Clickable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/lbl_donate"
                        app:drawableStartCompat="@drawable/ic_donate_24"
                        app:drawableTint="?attr/colorControlNormal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_licenses" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>



            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/spacing_medium">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/spacing_tiny">

                    <TextView
                        android:id="@+id/about_supporters"
                        style="@style/Widget.Auxio.TextView.Header"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/lbl_supporters"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <TextView
                        android:id="@+id/about_sup_mark_pitblado"
                        style="@style/Widget.Auxio.TextView.Icon.Clickable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/sup_mark_pitblado"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:drawableStartCompat="@drawable/ic_person_24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_licenses" />

                    <TextView
                        android:id="@+id/about_supporters_promo"
                        style="@style/Widget.Auxio.TextView.Icon.Clickable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/lng_supporters_promo"
                        android:textStyle="italic"
                        android:textAppearance="@style/TextAppearance.Auxio.BodyMedium"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_licenses" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/spacing_medium">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/spacing_tiny">

                    <TextView
                        android:id="@+id/about_feedback"
                        style="@style/Widget.Auxio.TextView.Header"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/lbl_feedback"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <TextView
                        android:id="@+id/about_feedback_github"
                        style="@style/Widget.Auxio.TextView.Icon.Clickable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/lbl_github"
                        app:drawableStartCompat="@drawable/ic_feature_request_24"
                        app:drawableTint="?attr/colorControlNormal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_licenses" />

                    <TextView
                        android:id="@+id/about_feedback_email"
                        style="@style/Widget.Auxio.TextView.Icon.Clickable"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/lbl_email"
                        app:drawableStartCompat="@drawable/ic_email_24"
                        app:drawableTint="?attr/colorControlNormal"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_licenses" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/spacing_medium">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/spacing_tiny">

                    <TextView
                        android:id="@+id/about_library_counts"
                        style="@style/Widget.Auxio.TextView.Header"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/lbl_library_counts"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                    <TextView
                        android:id="@+id/about_song_count"
                        style="@style/Widget.Auxio.TextView.Icon"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:drawableStartCompat="@drawable/ic_song_24"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_licenses"
                        tools:text="Songs Loaded: 1616" />

                    <TextView
                        android:id="@+id/about_album_count"
                        style="@style/Widget.Auxio.TextView.Icon"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:drawableStartCompat="@drawable/ic_album_24"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_licenses"
                        tools:text="Albums Loaded: 1616" />

                    <TextView
                        android:id="@+id/about_artist_count"
                        style="@style/Widget.Auxio.TextView.Icon"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:drawableStartCompat="@drawable/ic_artist_24"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_licenses"
                        tools:text="Artists Loaded: 1616" />

                    <TextView
                        android:id="@+id/about_genre_count"
                        style="@style/Widget.Auxio.TextView.Icon"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:drawableStartCompat="@drawable/ic_genre_24"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_licenses"
                        tools:text="Genres Loaded: 1616" />

                    <TextView
                        android:id="@+id/about_total_duration"
                        style="@style/Widget.Auxio.TextView.Icon"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:drawableStartCompat="@drawable/ic_time_24"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/about_licenses"
                        tools:text="Total duration: 16:16:16" />

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>