// Copyright 2024 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
apply from: '../../constants.gradle'
apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    namespace 'androidx.media3.demo.compose'

    compileSdk project.ext.compileSdkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    defaultConfig {
        versionName project.ext.releaseVersion
        versionCode project.ext.releaseVersionCode
        minSdkVersion project.ext.minSdkVersion
        targetSdkVersion project.ext.appTargetSdkVersion
    }

    buildTypes {
        release {
            shrinkResources true
            minifyEnabled true
            signingConfig signingConfigs.debug
        }
        debug {
            jniDebuggable = true
        }
    }

    lintOptions {
        // The demo app isn't indexed, and doesn't have translations.
        disable 'GoogleAppIndexingWarning','MissingTranslation'
    }
    buildFeatures {
        viewBinding true
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.3"
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
}

dependencies {
    def composeBom = platform('androidx.compose:compose-bom:2024.05.00')
    implementation composeBom

    implementation 'androidx.activity:activity-compose:1.9.0'
    implementation 'androidx.compose.foundation:foundation-android:1.6.7'
    implementation 'androidx.compose.material3:material3-android:1.2.1'
    implementation 'com.google.android.material:material:' + androidxMaterialVersion

    implementation project(modulePrefix + 'lib-exoplayer')

    // For detecting and debugging leaks only. LeakCanary is not needed for demo app to work.
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:' + leakCanaryVersion

    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:' + kotlinxCoroutinesVersion
    testImplementation 'org.robolectric:robolectric:' + robolectricVersion
    testImplementation project(modulePrefix + 'test-utils')

}
