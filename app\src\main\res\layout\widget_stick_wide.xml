<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@android:id/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:baselineAligned="false"
    android:orientation="horizontal"
    android:theme="@style/Theme.Auxio.Widget">


    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/ui_widget_rectangle_button_bg">
            <android.widget.ImageButton
                android:id="@+id/widget_repeat"
                style="@style/Widget.Auxio.MaterialButton.AppWidget"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/desc_change_repeat"
                android:src="@drawable/ic_repeat_off_24" />
    </FrameLayout>

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/ui_widget_rectangle_button_bg">

        <android.widget.ImageButton
            android:id="@+id/widget_skip_prev"
            style="@style/Widget.Auxio.MaterialButton.AppWidget"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/desc_skip_prev"
            android:src="@drawable/ic_skip_prev_24" />
    </FrameLayout>


    <android.widget.ImageButton
        android:id="@+id/widget_play_pause"
        style="@style/Widget.Auxio.MaterialButton.AppWidget.PlayPause"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:contentDescription="@string/desc_play_pause"
        android:src="@drawable/ic_play_24" />

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/ui_widget_rectangle_button_bg">

        <android.widget.ImageButton
            android:id="@+id/widget_skip_next"
            style="@style/Widget.Auxio.MaterialButton.AppWidget"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:contentDescription="@string/desc_skip_next"
            android:src="@drawable/ic_skip_next_24" />

    </FrameLayout>

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/ui_widget_rectangle_button_bg">

            <android.widget.ImageButton
                android:id="@+id/widget_shuffle"
                style="@style/Widget.Auxio.MaterialButton.AppWidget"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="@string/desc_shuffle"
                android:src="@drawable/ic_shuffle_off_24" />
    </FrameLayout>

</LinearLayout>


