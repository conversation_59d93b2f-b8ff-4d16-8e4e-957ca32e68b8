Auxio는 다른 음악 플레이어에 존재하는 쓸모없는 기능 없이, 빠르고 안정적인 UI/UX를 갖춘 로컬 음악 플레이어입니다. 최신 미디어 재생 라이브러리를 기반으로 구축된 Auxio는 오래된 Android 기능을 사용하는 다른 앱에 비해 뛰어난 라이브러리 지원과 음악 재생 품질을 제공합니다. 즉, <b>음악을 제대로 재생하는 플레이어입니다.</b>

<b>기능</b>

- Media3 ExoPlayer 기반
- 머티리얼 디자인 가이드라인을 따르는 깔끔한 UI
- 엣지 케이스보다는 사용 편의성을 우선시한 UX
- 사용자 정의 가능한 동작
- 디스크 번호, 여러 아티스트, 릴리스 유형 지원,
정확한/원본 날짜, 정렬 태그 등 지원
- 아티스트와 앨범 아티스트를 통합한 고급 아티스트 시스템
- SD 카드를 지원하는 폴더 관리 기능
- 안정적인 재생 목록 기능
- 이전 재생 상태 기억
- Android Auto 지원
- 자동 갭리스 재생 지원
- ReplayGain 완벽 지원 (MP3, FLAC, OGG, OPUS, MP4)
- 외부 이퀄라이저 지원 (Wavelet 등)
- Edge-to-edge
- 파일 내장 앨범 커버 지원
- 검색 기능
- 헤드셋 연결 시 자동 재생
- 크기에 따라 자동으로 조정되는 세련된 위젯
- 인터넷 사용 없음
- 둥글지 않은 앨범 커버 (기본값)
