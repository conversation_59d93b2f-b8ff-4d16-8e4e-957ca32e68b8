<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:orientation="vertical"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:background="@color/player_background"
  tools:context=".PlayerActivity">

  <androidx.media3.ui.PlayerView
    android:id="@+id/player_view"
    android:background="@color/player_background"
    android:layout_width="match_parent"
    android:layout_height="300dp"
    app:artwork_display_mode="fill"
    app:default_artwork="@drawable/artwork_placeholder"
    app:repeat_toggle_modes="one|all"
    app:show_shuffle_button="true"
    app:shutter_background_color="@color/player_background" />

  <TextView
    android:id="@+id/media_artist"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="10dp"
    android:paddingStart="10dp"
    android:paddingTop="10dp"
    android:textColor="@color/white"
    android:textSize="14sp"/>

  <TextView
    android:id="@+id/media_title"
    android:ellipsize="end"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:maxLines="1"
    android:paddingLeft="10dp"
    android:paddingBottom="10dp"
    android:paddingStart="10dp"
    android:textColor="@color/white"
    android:textSize="20sp" />

  <View
    android:background="@color/divider"
    android:layout_height="1dp"
    android:layout_width="match_parent" />

  <ListView
    android:id="@+id/current_playing_list"
    android:divider="@drawable/divider"
    android:dividerHeight="1px"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" />

</LinearLayout>
