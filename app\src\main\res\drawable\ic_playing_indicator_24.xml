<?xml version="1.0" encoding="utf-8"?>
<animation-list xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">

    <!--
    I kind of like Spotify's little equalizer playing indicator, so I tried to make
    the material symbols equalizer icon animate in a similar way. I got the best
    results doing it frame by frame, but to avoid cluttering the drawable folder I
    bundled them up with AAPT inlining, hence this monster of a file.
    -->

    <!-- Frame 1 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="M10,20h4L14,4h-4v16zM4,20h4v-8L4,12v8zM16,9v11h4L20,9h-4z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 2 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,3.9997559 c 0,5.3333524 0,10.6667051 0,16.0000571 1.3334246,0 2.6668486,0 4.0002726,0 0,-5.333352 0,-10.6667047 0,-16.0000571 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,10.999845 c 0,2.999989 0,5.999979 0,8.999968 1.3334242,0 2.6668484,0 4.0002726,0 0,-2.999989 0,-5.999979 0,-8.999968 -1.3334242,0 -2.6668484,0 -4.0002726,0 z m 12.0003011,0 c 0,2.999989 0,5.999979 0,8.999968 1.333252,0 2.666504,0 3.999756,0 0,-2.999989 0,-5.999979 0,-8.999968 -1.333252,0 -2.666504,0 -3.999756,0 z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 3 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,5.0002116 c 0,4.9998674 0,9.9997344 0,14.9996014 1.3334246,0 2.6668486,0 4.0002726,0 0,-4.999867 0,-9.999734 0,-14.9996014 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,9.9999064 c 0,3.3333026 0,6.6666046 0,9.9999066 1.3334242,0 2.6668484,0 4.0002726,0 0,-3.333302 0,-6.666604 0,-9.9999066 -1.3334242,0 -2.6668484,0 -4.0002726,0 z M 16.000057,14.000179 c 0,1.999878 0,3.999756 0,5.999634 1.333252,0 2.666504,0 3.999756,0 0,-1.999878 0,-3.999756 0,-5.999634 -1.333252,0 -2.666504,0 -3.999756,0 z " />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 4 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,5.0002116 c 0,4.9998674 0,9.9997344 0,14.9996014 1.3334246,0 2.6668486,0 4.0002726,0 0,-4.999867 0,-9.999734 0,-14.9996014 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,9 c 0,3.666604 0,7.333209 0,10.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-3.666604 0,-7.333209 0,-10.999813 C 6.6666043,9 5.3331801,9 3.9997559,9 Z m 12.0003011,7 c 0,1.333271 0,2.666542 0,3.999813 1.333252,0 2.666504,0 3.999756,0 0,-1.333271 0,-2.666542 0,-3.999813 -1.333252,0 -2.666504,0 -3.999756,0 z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 5 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,7 c 0,4.333271 0,8.666542 0,12.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-4.333271 0,-8.666542 0,-12.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z m -6.0001505,4 c 0,2.999938 0,5.999875 0,8.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-2.999938 0,-5.999875 0,-8.999813 -1.3334242,0 -2.6668484,0 -4.0002726,0 z m 12.0003011,6 c 0,0.999938 0,1.999875 0,2.999813 1.333252,0 2.666504,0 3.999756,0 0,-0.999938 0,-1.999875 0,-2.999813 -1.333252,0 -2.666504,0 -3.999756,0 z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 6 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,9 c 0,3.666604 0,7.333209 0,10.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-3.666604 0,-7.333209 0,-10.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z m -6.0001505,1 c 0,3.333271 0,6.666542 0,9.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-3.333271 0,-6.666542 0,-9.999813 -1.3334242,0 -2.6668484,0 -4.0002726,0 z M 16,18 c 0.0000190,0.666604 0.0000380,1.333209 0.0000570,1.999813 1.333252,0 2.666504,0 3.999756,0 0,-0.666604 0,-1.333209 0,-1.999813 C 18.666542,18 17.333271,18 16,18 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 7 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,11 c 0,2.999938 0,5.999875 0,8.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-2.999938 0,-5.999875 0,-8.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,9 c 0,3.666604 0,7.333209 0,10.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-3.666604 0,-7.333209 0,-10.999813 C 6.6666043,9 5.3331801,9 3.9997559,9 Z M 16,18 c 0.0000190,0.666604 0.0000380,1.333209 0.0000570,1.999813 1.333252,0 2.666504,0 3.999756,0 0,-0.666604 0,-1.333209 0,-1.999813 C 18.666542,18 17.333271,18 16,18 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 8 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,13 c 0,2.333271 0,4.666542 0,6.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-2.333271 0,-4.666542 0,-6.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,8 c 0,3.999938 0,7.999875 0,11.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-3.999938 0,-7.999875 0,-11.999813 C 6.6666043,8 5.3331801,8 3.9997559,8 Z M 16,18 c 0.0000190,0.666604 0.0000380,1.333209 0.0000570,1.999813 1.333252,0 2.666504,0 3.999756,0 0,-0.666604 0,-1.333209 0,-1.999813 C 18.666542,18 17.333271,18 16,18 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 9 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,16 c 0,1.333271 0,2.666542 0,3.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-1.333271 0,-2.666542 0,-3.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,7 c 0,4.333271 0,8.666542 0,12.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-4.333271 0,-8.666542 0,-12.999813 C 6.6666043,7 5.3331801,7 3.9997559,7 Z M 16,17 c 0.0000190,0.999938 0.0000380,1.999875 0.0000570,2.999813 1.333252,0 2.666504,0 3.999756,0 0,-0.999938 0,-1.999875 0,-2.999813 C 18.666542,17 17.333271,17 16,17 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 10 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,17 c 0,0.999938 0,1.999875 0,2.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-0.999938 0,-1.999875 0,-2.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,6 c 0,4.666604 0,9.333209 0,13.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-4.666604 0,-9.333209 0,-13.999813 C 6.6666043,6 5.3331801,6 3.9997559,6 Z M 16,17 c 0.0000190,0.999938 0.0000380,1.999875 0.0000570,2.999813 1.333252,0 2.666504,0 3.999756,0 0,-0.999938 0,-1.999875 0,-2.999813 C 18.666542,17 17.333271,17 16,17 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 11 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,18 c 0,0.666604 0,1.333209 0,1.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-0.666604 0,-1.333209 0,-1.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,5 c 0,4.9999377 0,9.999875 0,14.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-4.999938 0,-9.9998753 0,-14.999813 C 6.6666043,5 5.3331801,5 3.9997559,5 Z M 16,15 c 0.0000190,1.666604 0.0000380,3.333209 0.0000570,4.999813 1.333252,0 2.666504,0 3.999756,0 0,-1.666604 0,-3.333209 0,-4.999813 C 18.666542,15 17.333271,15 16,15 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 12 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,18 c 0,0.666604 0,1.333209 0,1.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-0.666604 0,-1.333209 0,-1.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,5 c 0,4.9999377 0,9.999875 0,14.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-4.999938 0,-9.9998753 0,-14.999813 C 6.6666043,5 5.3331801,5 3.9997559,5 Z M 16,14 c 0.0000190,1.999938 0.0000380,3.999875 0.0000570,5.999813 1.333252,0 2.666504,0 3.999756,0 0,-1.999938 0,-3.999875 0,-5.999813 C 18.666542,14 17.333271,14 16,14 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 13 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,17 c 0,0.999938 0,1.999875 0,2.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-0.999938 0,-1.999875 0,-2.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,4 c 0,5.333271 0,10.666542 0,15.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-5.333271 0,-10.666542 0,-15.999813 C 6.6666043,4 5.3331801,4 3.9997559,4 Z M 16,12 c 0.0000190,2.666604 0.0000380,5.333209 0.0000570,7.999813 1.333252,0 2.666504,0 3.999756,0 0,-2.666604 0,-5.333209 0,-7.999813 C 18.666542,12 17.333271,12 16,12 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 14 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,17 c 0,0.999938 0,1.999875 0,2.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-0.999938 0,-1.999875 0,-2.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,4 c 0,5.333271 0,10.666542 0,15.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-5.333271 0,-10.666542 0,-15.999813 C 6.6666043,4 5.3331801,4 3.9997559,4 Z M 16,11 c 0.0000190,2.999938 0.0000380,5.999875 0.0000570,8.999813 1.333252,0 2.666504,0 3.999756,0 0,-2.999938 0,-5.999875 0,-8.999813 C 18.666542,11 17.333271,11 16,11 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 15 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,17 c 0,0.999938 0,1.999875 0,2.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-0.999938 0,-1.999875 0,-2.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,4 c 0,5.333271 0,10.666542 0,15.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-5.333271 0,-10.666542 0,-15.999813 C 6.6666043,4 5.3331801,4 3.9997559,4 Z M 16,10 c 0.0000190,3.333271 0.0000380,6.666542 0.0000570,9.999813 1.333252,0 2.666504,0 3.999756,0 0,-3.333271 0,-6.666542 0,-9.999813 C 18.666542,10 17.333271,10 16,10 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 16 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,15 c 0,1.666604 0,3.333209 0,4.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-1.666604 0,-3.333209 0,-4.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,5 c 0,4.9999377 0,9.999875 0,14.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-4.999938 0,-9.9998753 0,-14.999813 C 6.6666043,5 5.3331801,5 3.9997559,5 Z M 16,10 c 0.0000190,3.333271 0.0000380,6.666542 0.0000570,9.999813 1.333252,0 2.666504,0 3.999756,0 0,-3.333271 0,-6.666542 0,-9.999813 C 18.666542,10 17.333271,10 16,10 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 17 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,14 c 0,1.999938 0,3.999875 0,5.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-1.999938 0,-3.999875 0,-5.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,5 c 0,4.9999377 0,9.999875 0,14.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-4.999938 0,-9.9998753 0,-14.999813 C 6.6666043,5 5.3331801,5 3.9997559,5 Z M 16,9 c 0.0000190,3.666604 0.0000380,7.333209 0.0000570,10.999813 1.333252,0 2.666504,0 3.999756,0 0,-3.666604 0,-7.333209 0,-10.999813 C 18.666542,9 17.333271,9 16,9 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 18 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,14 c 0,1.999938 0,3.999875 0,5.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-1.999938 0,-3.999875 0,-5.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 3.9997559,5 c 0,4.9999377 0,9.999875 0,14.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-4.999938 0,-9.9998753 0,-14.999813 C 6.6666043,5 5.3331801,5 3.9997559,5 Z M 16,9 c 0.0000190,3.666604 0.0000380,7.333209 0.0000570,10.999813 1.333252,0 2.666504,0 3.999756,0 0,-3.666604 0,-7.333209 0,-10.999813 C 18.666542,9 17.333271,9 16,9 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 19 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 13.999663,10 c 0,3.333271 0,6.666542 0,9.999813 -1.333424,0 -2.666849,0 -4.0002731,0 0,-3.333271 0,-6.666542 0,-9.999813 1.3334241,0 2.6668491,0 4.0002731,0 z m 5.999906,0 c 0.0000810,3.333271 0.0001630,6.666542 0.0002440,9.999813 -1.333424,0 -2.666849,0 -4.000273,0 0,-3.333271 0,-6.666542 0,-9.999813 1.333343,0 2.666686,0 4.000029,0 z M 7.9995689,9 c -0.0000190,3.666604 -0.0000380,7.333209 -0.0000570,10.999813 -1.333252,0 -2.666504,0 -3.999756,0 0,-3.666604 0,-7.333209 0,-10.999813 1.333271,0 2.666542,0 3.999813,0 z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 20 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,10 c 0,3.333271 0,6.666542 0,9.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-3.333271 0,-6.666542 0,-9.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 4,13 c -0.0000814,2.333271 -0.0001627,4.666542 -0.0002441,6.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-2.333271 0,-4.666542 0,-6.999813 C 6.6666857,13 5.3333428,13 4,13 Z M 16,9 c 0.0000190,3.666604 0.0000380,7.333209 0.0000570,10.999813 1.333252,0 2.666504,0 3.999756,0 0,-3.666604 0,-7.333209 0,-10.999813 C 18.666542,9 17.333271,9 16,9 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 21 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,9 c 0,3.666604 0,7.333209 0,10.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-3.666604 0,-7.333209 0,-10.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 4,15 c -0.0000814,1.666604 -0.0001627,3.333209 -0.0002441,4.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-1.666604 0,-3.333209 0,-4.999813 C 6.6666857,15 5.3333428,15 4,15 Z M 16,8 c 0.0000190,3.999938 0.0000380,7.999875 0.0000570,11.999813 1.333252,0 2.666504,0 3.999756,0 0,-3.999938 0,-7.999875 0,-11.999813 C 18.666542,8 17.333271,8 16,8 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 22 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,10 c 0,3.333271 0,6.666542 0,9.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-3.333271 0,-6.666542 0,-9.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 4,17 c -0.0000814,0.999938 -0.0001627,1.999875 -0.0002441,2.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-0.999938 0,-1.999875 0,-2.999813 C 6.6666857,17 5.3333428,17 4,17 Z M 16,7 c 0.0000190,4.333271 0.0000380,8.666542 0.0000570,12.999813 1.333252,0 2.666504,0 3.999756,0 0,-4.333271 0,-8.666542 0,-12.999813 C 18.666542,7 17.333271,7 16,7 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 23 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,9 c 0,3.666604 0,7.333209 0,10.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-3.666604 0,-7.333209 0,-10.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 4,17 c -0.0000814,0.999938 -0.0001627,1.999875 -0.0002441,2.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-0.999938 0,-1.999875 0,-2.999813 C 6.6666857,17 5.3333428,17 4,17 Z M 16,6 c 0.0000190,4.666604 0.0000380,9.333209 0.0000570,13.999813 1.333252,0 2.666504,0 3.999756,0 0,-4.666604 0,-9.333209 0,-13.999813 C 18.666542,6 17.333271,6 16,6 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 24 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,9 c 0,3.666604 0,7.333209 0,10.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-3.666604 0,-7.333209 0,-10.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 4,18 c -0.0000814,0.666604 -0.0001627,1.333209 -0.0002441,1.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-0.666604 0,-1.333209 0,-1.999813 C 6.6666857,18 5.3333428,18 4,18 Z M 16,5 c 0.0000190,4.9999377 0.0000380,9.999875 0.0000570,14.999813 1.333252,0 2.666504,0 3.999756,0 0,-4.999938 0,-9.9998753 0,-14.999813 C 18.666542,5 17.333271,5 16,5 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 25 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,9 c 0,3.666604 0,7.333209 0,10.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-3.666604 0,-7.333209 0,-10.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 4,18 c -0.0000814,0.666604 -0.0001627,1.333209 -0.0002441,1.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-0.666604 0,-1.333209 0,-1.999813 C 6.6666857,18 5.3333428,18 4,18 Z M 16,5 c 0.0000190,4.9999377 0.0000380,9.999875 0.0000570,14.999813 1.333252,0 2.666504,0 3.999756,0 0,-4.999938 0,-9.9998753 0,-14.999813 C 18.666542,5 17.333271,5 16,5 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 26 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 9.9999064,8 c 0,3.999938 0,7.999875 0,11.999813 1.3334246,0 2.6668486,0 4.0002726,0 0,-3.999938 0,-7.999875 0,-11.999813 -1.333424,0 -2.666848,0 -4.0002726,0 z M 4,17 c -0.0000814,0.999938 -0.0001627,1.999875 -0.0002441,2.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-0.999938 0,-1.999875 0,-2.999813 C 6.6666857,17 5.3333428,17 4,17 Z M 16,4 c 0.0000190,5.333271 0.0000380,10.666542 0.0000570,15.999813 1.333252,0 2.666504,0 3.999756,0 0,-5.333271 0,-10.666542 0,-15.999813 C 18.666542,4 17.333271,4 16,4 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 27 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 10,7 c -0.0000312,4.333271 -0.0000624,8.666542 -0.0000936,12.999813 1.3334246,0 2.6668486,0 4.0002726,0 C 14.000119,15.666542 14.00006,11.333271 14,7 12.666667,7 11.333333,7 10,7 Z M 4,17 c -0.0000814,0.999938 -0.0001627,1.999875 -0.0002441,2.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-0.999938 0,-1.999875 0,-2.999813 C 6.6666857,17 5.3333428,17 4,17 Z M 16,4 c 0.0000190,5.333271 0.0000380,10.666542 0.0000570,15.999813 1.333252,0 2.666504,0 3.999756,0 0,-5.333271 0,-10.666542 0,-15.999813 C 18.666542,4 17.333271,4 16,4 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 28 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 10,6 c -0.0000312,4.666604 -0.0000624,9.333209 -0.0000936,13.999813 1.3334246,0 2.6668486,0 4.0002726,0 C 14.000119,15.333209 14.00006,10.666604 14,6 12.666667,6 11.333333,6 10,6 Z M 4,16 c -0.0000814,1.333271 -0.0001627,2.666542 -0.0002441,3.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-1.333271 0,-2.666542 0,-3.999813 C 6.6666857,16 5.3333428,16 4,16 Z M 16,5 c 0.0000190,4.9999377 0.0000380,9.999875 0.0000570,14.999813 1.333252,0 2.666504,0 3.999756,0 0,-4.999938 0,-9.9998753 0,-14.999813 C 18.666542,5 17.333271,5 16,5 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 29 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 10,5 c -0.0000312,4.9999377 -0.0000624,9.999875 -0.0000936,14.999813 1.3334246,0 2.6668486,0 4.0002726,0 C 14.000119,14.999875 14.00006,9.9999377 14,5 12.666667,5 11.333333,5 10,5 Z m -6,9 c -0.0000814,1.999938 -0.0001627,3.999875 -0.0002441,5.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-1.999938 0,-3.999875 0,-5.999813 C 6.6666857,14 5.3333428,14 4,14 Z M 16,5 c 0.0000190,4.9999377 0.0000380,9.999875 0.0000570,14.999813 1.333252,0 2.666504,0 3.999756,0 0,-4.999938 0,-9.9998753 0,-14.999813 C 18.666542,5 17.333271,5 16,5 Z" />
            </vector>
        </aapt:attr>
    </item>

    <!-- Frame 30 -->
    <item android:duration="30">
        <aapt:attr name="android:drawable">
            <vector
                android:width="24dp"
                android:height="24dp"
                android:tint="?attr/colorControlNormal"
                android:viewportWidth="24"
                android:viewportHeight="24">
                <path
                    android:fillColor="@android:color/white"
                    android:pathData="m 10,5 c -0.0000312,4.9999377 -0.0000624,9.999875 -0.0000936,14.999813 1.3334246,0 2.6668486,0 4.0002726,0 C 14.000119,14.999875 14.00006,9.9999377 14,5 12.666667,5 11.333333,5 10,5 Z m -6,8 c -0.0000814,2.333271 -0.0001627,4.666542 -0.0002441,6.999813 1.3334242,0 2.6668484,0 4.0002726,0 0,-2.333271 0,-4.666542 0,-6.999813 C 6.6666857,13 5.3333428,13 4,13 Z M 16,6 c 0.0000190,4.666604 0.0000380,9.333209 0.0000570,13.999813 1.333252,0 2.666504,0 3.999756,0 0,-4.666604 0,-9.333209 0,-13.999813 C 18.666542,6 17.333271,6 16,6 Z" />
            </vector>
        </aapt:attr>
    </item>
</animation-list>


