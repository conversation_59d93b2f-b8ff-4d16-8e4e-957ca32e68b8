<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--
    Typography is derived from Inter and it's size-dependent spacing calculation. Auxio does not
    completely adhere to the typography system, sometimes having two variants of a given style in
    order to bypass the restrictive nature of the default typography.
    -->

    <!--
    Display styles are not used in Auxio.
    -->
    <style name="TextAppearance.Auxio.DisplayLarge" parent="TextAppearance.Material3.DisplayLarge">
        <item name="fontFamily">@font/inter_semibold</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">-0.0192807018</item>
    </style>

    <style name="TextAppearance.Auxio.DisplayMedium" parent="TextAppearance.Material3.DisplayMedium">
        <item name="fontFamily">@font/inter_regular</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">-0.0192222222</item>
    </style>

    <style name="TextAppearance.Auxio.DisplaySmall" parent="TextAppearance.Material3.DisplaySmall">
        <item name="fontFamily">@font/inter_regular</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">-0.0189444444</item>
    </style>

    <!--
    Headline styles are similar to title styles, but are not used as often.
    -->
    <style name="TextAppearance.Auxio.HeadlineLarge" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="fontFamily">@font/inter_semibold</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">-0.0185625</item>
    </style>

    <style name="TextAppearance.Auxio.HeadlineMedium" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="fontFamily">@font/inter_semibold</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">-0.0180714286</item>
    </style>

    <style name="TextAppearance.Auxio.HeadlineSmall" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="fontFamily">@font/inter_semibold</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">-0.0165833333</item>
    </style>

    <!--
    Title styles are often used for headers, dividing elements, and items.
    -->
    <style name="TextAppearance.Auxio.TitleLarge" parent="TextAppearance.Material3.TitleLarge">
        <item name="fontFamily">@font/inter_semibold</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">-0.01518181818</item>
    </style>

    <style name="TextAppearance.Auxio.TitleLargeLowEmphasis" parent="TextAppearance.Material3.TitleLarge">
        <item name="fontFamily">@font/inter_regular</item>
        <item name="android:fontFamily">@font/inter_regular</item>
        <item name="android:textStyle">normal</item>
        <item name="android:letterSpacing">-0.01818181818</item>
    </style>

    <style name="TextAppearance.Auxio.TitleMedium" parent="TextAppearance.Material3.TitleMedium">
        <item name="fontFamily">@font/inter_regular</item>
        <item name="android:fontFamily">@font/inter_regular</item>
        <item name="android:textStyle">normal</item>
        <item name="android:letterSpacing">-0.01125</item>
    </style>

    <style name="TextAppearance.Auxio.TitleSmall" parent="TextAppearance.Material3.TitleSmall">
        <item name="fontFamily">@font/inter_semibold</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">-0.003</item>
    </style>

    <!-- Label styles are used for elements that can be interacted with. -->
    <style name="TextAppearance.Auxio.LabelLarge" parent="TextAppearance.Material3.LabelLarge">
        <item name="fontFamily">@font/inter_semibold</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">-0.003</item>
    </style>

    <style name="TextAppearance.Auxio.LabelMedium" parent="TextAppearance.Material3.LabelMedium">
        <item name="fontFamily">@font/inter_semibold</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
        <item name="android:textStyle">normal</item>
        <item name="android:letterSpacing">0.003</item>
    </style>

    <style name="TextAppearance.Auxio.LabelSmall" parent="TextAppearance.Material3.LabelSmall">
        <item name="fontFamily">@font/inter_semibold</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
        <item name="android:textStyle">normal</item>
        <item name="android:letterSpacing">0.0075454545</item>
    </style>

    <!--
    The body typeface is used for secondary and/or singular UI elements.
    -->
    <style name="TextAppearance.Auxio.BodyLarge" parent="TextAppearance.Material3.BodyLarge">
        <item name="fontFamily">@font/inter_regular</item>
        <item name="android:fontFamily">@font/inter_regular</item>
        <item name="android:textStyle">normal</item>
        <item name="android:letterSpacing">-0.01125</item>
    </style>

    <style name="TextAppearance.Auxio.BodyMedium" parent="TextAppearance.Material3.BodyMedium">
        <item name="fontFamily">@font/inter_regular</item>
        <item name="android:fontFamily">@font/inter_regular</item>
        <item name="android:textStyle">normal</item>
        <item name="android:letterSpacing">-0.0064285714</item>
    </style>

    <style name="TextAppearance.Auxio.BodySmall" parent="TextAppearance.Material3.BodySmall">
        <item name="fontFamily">@font/inter_regular</item>
        <item name="android:fontFamily">@font/inter_regular</item>
        <item name="android:textStyle">normal</item>
        <item name="android:letterSpacing">0.0008333333</item>
    </style>

</resources>