// Copyright (C) 2020 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
apply from: "$gradle.ext.androidxMediaSettingsDir/common_library_config.gradle"

android {
    namespace 'androidx.media3.datasource'

    buildTypes {
        debug {
            testCoverageEnabled = true
        }
    }

    sourceSets {
        androidTest.assets.srcDir '../test_data/src/test/assets'
        test.assets.srcDir '../test_data/src/test/assets'
    }

    publishing {
        singleVariant('release') {
            withSourcesJar()
        }
    }
}

dependencies {
    implementation project(modulePrefix + 'lib-common')
    implementation project(modulePrefix + 'lib-database')
    implementation 'androidx.annotation:annotation:' + androidxAnnotationVersion
    implementation 'androidx.exifinterface:exifinterface:' + androidxExifInterfaceVersion
    compileOnly 'com.google.code.findbugs:jsr305:' + jsr305Version
    compileOnly 'com.google.errorprone:error_prone_annotations:' + errorProneVersion
    compileOnly 'org.checkerframework:checker-qual:' + checkerframeworkVersion
    compileOnly 'org.jetbrains.kotlin:kotlin-annotations-jvm:' + kotlinAnnotationsVersion
    androidTestImplementation 'androidx.test:runner:' + androidxTestRunnerVersion
    androidTestImplementation 'com.linkedin.dexmaker:dexmaker:' + dexmakerVersion
    androidTestImplementation 'com.linkedin.dexmaker:dexmaker-mockito:' + dexmakerVersion
    androidTestImplementation 'com.squareup.okhttp3:mockwebserver:' + okhttpVersion
    androidTestImplementation(project(modulePrefix + 'test-utils')) {
        exclude module: modulePrefix.substring(1) + 'library-core'
    }
    testImplementation 'org.mockito:mockito-core:' + mockitoVersion
    testImplementation 'androidx.test:core:' + androidxTestCoreVersion
    testImplementation 'androidx.test.ext:junit:' + androidxTestJUnitVersion
    testImplementation 'com.google.truth:truth:' + truthVersion
    testImplementation 'com.squareup.okhttp3:mockwebserver:' + okhttpVersion
    testImplementation 'org.robolectric:robolectric:' + robolectricVersion
    testImplementation project(modulePrefix + 'test-utils')
}

ext {
    releaseArtifactId = 'media3-datasource'
    releaseName = 'Media3 DataSource module'
}
apply from: '../../publish.gradle'
