<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    android:orientation="vertical"
    android:transitionGroup="true">

    <org.oxycblt.auxio.ui.CoordinatorAppBarLayout
        android:id="@+id/preferences_appbar"
        style="@style/Widget.Auxio.AppBarLayout"
        android:clickable="true"
        android:focusable="true"
        app:liftOnScroll="true">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/preferences_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:navigationIcon="@drawable/ic_back_24"
            app:title="@string/set_root_title" />

    </org.oxycblt.auxio.ui.CoordinatorAppBarLayout>

    <FrameLayout
        android:id="@android:id/list_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
