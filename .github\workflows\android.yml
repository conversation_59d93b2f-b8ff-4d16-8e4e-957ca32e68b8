name: Android CI

on:
  push:
    branches: []
  pull_request:
    branches: []
    
jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Install ninja-build
      run: sudo apt-get install -y ninja-build
    - name: Clone repository
      uses: actions/checkout@v4
    - name: Clone submodules
      run: git submodule update --init --recursive --remote
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        cache: gradle
    - name: <PERSON> execute permission for gradlew
      run: chmod +x gradlew
    - name: Check formatting with spotless
      run: ./gradlew spotlessCheck
    - name: Test musikr with Gradle
      run: ./gradlew musikr:testDebug
    - name: Build debug APK with Gradle
      run: ./gradlew app:packageDebug
    - name: Upload debug APK artifact
      uses: actions/upload-artifact@v4
      with:
        name: Auxio_Canary
        path: ./app/build/outputs/apk/debug/app-debug.apk
