/*
 * Copyright (c) 2023 Auxio Project
 * RippleFixMaterialButton.kt is part of Auxio.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
 
package org.oxycblt.auxio.ui

import android.content.Context
import android.util.AttributeSet
import androidx.annotation.AttrRes
import com.google.android.material.R
import com.google.android.material.button.MaterialButton
import org.oxycblt.auxio.util.fixDoubleRipple

/**
 * Fixes an issue where double ripples appear on [MaterialButton] from AppCompat 1.5 afterwards due
 * to a currently unfixed change.
 *
 * <AUTHOR> (OxygenCobalt)
 */
open class RippleFixMaterialButton
@JvmOverloads
constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = R.attr.materialButtonStyle
) : MaterialButton(context, attrs, defStyleAttr) {
    init {
        fixDoubleRipple()
    }
}
