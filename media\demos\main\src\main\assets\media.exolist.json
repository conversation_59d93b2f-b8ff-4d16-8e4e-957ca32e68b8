[{"name": "Clear DASH", "samples": [{"name": "HD (MP4, H264)", "uri": "https://storage.googleapis.com/wvmedia/clear/h264/tears/tears.mpd"}, {"name": "UHD (MP4, H264)", "uri": "https://storage.googleapis.com/wvmedia/clear/h264/tears/tears_uhd.mpd"}, {"name": "HD (MP4, H265)", "uri": "https://storage.googleapis.com/wvmedia/clear/hevc/tears/tears.mpd"}, {"name": "UHD (MP4, H265)", "uri": "https://storage.googleapis.com/wvmedia/clear/hevc/tears/tears_uhd.mpd"}, {"name": "HD (WebM, VP9)", "uri": "https://storage.googleapis.com/wvmedia/clear/vp9/tears/tears.mpd"}, {"name": "UHD (WebM, VP9)", "uri": "https://storage.googleapis.com/wvmedia/clear/vp9/tears/tears_uhd.mpd"}]}, {"name": "Widevine DASH (MP4, H264)", "samples": [{"name": "HD (cenc)", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=2015_tears&provider=widevine_test"}, {"name": "UHD (cenc)", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears_uhd.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=2015_tears&provider=widevine_test"}, {"name": "HD (cbcs)", "uri": "https://storage.googleapis.com/wvmedia/cbcs/h264/tears/tears_aes_cbcs.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=2015_tears&provider=widevine_test"}, {"name": "UHD (cbcs)", "uri": "https://storage.googleapis.com/wvmedia/cbcs/h264/tears/tears_aes_cbcs_uhd.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=2015_tears&provider=widevine_test"}, {"name": "Secure -> Clear -> Secure (cenc)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/widevine/tears_enc_clear_enc.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=2015_tears&provider=widevine_test", "drm_session_for_clear_content": true}]}, {"name": "Widevine DASH (WebM, VP9)", "samples": [{"name": "HD (cenc, full-sample)", "uri": "https://storage.googleapis.com/wvmedia/cenc/vp9/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=2015_tears&provider=widevine_test"}, {"name": "UHD (cenc, full-sample)", "uri": "https://storage.googleapis.com/wvmedia/cenc/vp9/tears/tears_uhd.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=2015_tears&provider=widevine_test"}, {"name": "HD (cenc, sub-sample)", "uri": "https://storage.googleapis.com/wvmedia/cenc/vp9/subsample/24fps/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=2015_tears&provider=widevine_test"}, {"name": "UHD (cenc, sub-sample)", "uri": "https://storage.googleapis.com/wvmedia/cenc/vp9/subsample/24fps/tears/tears_uhd.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=2015_tears&provider=widevine_test"}]}, {"name": "Widevine DASH (MP4, H265)", "samples": [{"name": "HD (cenc)", "uri": "https://storage.googleapis.com/wvmedia/cenc/hevc/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=2015_tears&provider=widevine_test"}, {"name": "UHD (cenc)", "uri": "https://storage.googleapis.com/wvmedia/cenc/hevc/tears/tears_uhd.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=2015_tears&provider=widevine_test"}]}, {"name": "Widevine DASH (policy tests)", "samples": [{"name": "SW secure crypto (L3)", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_SW_SECURE_CRYPTO&provider=widevine_test"}, {"name": "SW secure decode", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_SW_SECURE_DECODE&provider=widevine_test"}, {"name": "HW secure crypto", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_HW_SECURE_CRYPTO&provider=widevine_test"}, {"name": "HW secure decode", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_HW_SECURE_DECODE&provider=widevine_test"}, {"name": "HW secure all (L1)", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_HW_SECURE_ALL&provider=widevine_test"}, {"name": "20s license with renewal", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_CAN_RENEW&provider=widevine_test"}, {"name": "30s license (fails at ~30s)", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_CAN_RENEW_FALSE_LICENSE_30S_PLAYBACK_30S&provider=widevine_test"}, {"name": "HDCP not required", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_SW_SECURE_CRYPTO_HDCP_NONE&provider=widevine_test"}, {"name": "HDCP 1.0 required", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_SW_SECURE_CRYPTO_HDCP_V1&provider=widevine_test"}, {"name": "HDCP 2.0 required", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_SW_SECURE_CRYPTO_HDCP_V2&provider=widevine_test"}, {"name": "HDCP 2.1 required", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_SW_SECURE_CRYPTO_HDCP_V2_1&provider=widevine_test"}, {"name": "HDCP 2.2 required", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_SW_SECURE_CRYPTO_HDCP_V2_2&provider=widevine_test"}, {"name": "HDCP no digital output", "uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_SW_SECURE_CRYPTO_HDCP_NO_DIGITAL_OUTPUT&provider=widevine_test"}]}, {"name": "60fps DASH", "samples": [{"name": "HD (MP4, H264, Clear)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/60fps/bbb-clear-1080/manifest.mpd"}, {"name": "4K (MP4, H264, Clear)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/60fps/bbb-clear-2160/manifest.mpd"}, {"name": "HD (<PERSON>4, H264, <PERSON><PERSON><PERSON> cenc)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/60fps/bbb-wv-1080/manifest.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?provider=widevine_test"}, {"name": "4K (<PERSON>4, <PERSON>264, <PERSON><PERSON><PERSON> cenc)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/60fps/bbb-wv-2160/manifest.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?provider=widevine_test"}]}, {"name": "DASH - Multiple base URLs", "samples": [{"name": "DASH - Multiple base URLs", "uri": "https://storage.googleapis.com/exoplayer-test-media-0/dash-multiple-base-urls/manifest.mpd"}, {"name": "DASH - Multiple base URLs (fail over)", "uri": "https://storage.googleapis.com/exoplayer-test-media-0/dash-multiple-base-urls/manifest-failover.mpd"}]}, {"name": "HLS", "samples": [{"name": "Apple 4x3 basic stream (TS)", "uri": "https://devstreaming-cdn.apple.com/videos/streaming/examples/bipbop_4x3/bipbop_4x3_variant.m3u8"}, {"name": "Apple 16x9 basic stream (TS)", "uri": "https://devstreaming-cdn.apple.com/videos/streaming/examples/bipbop_16x9/bipbop_16x9_variant.m3u8"}, {"name": "Apple multivariant playlist advanced (TS)", "uri": "https://devstreaming-cdn.apple.com/videos/streaming/examples/img_bipbop_adv_example_ts/master.m3u8"}, {"name": "Apple multivariant playlist advanced (FMP4)", "uri": "https://devstreaming-cdn.apple.com/videos/streaming/examples/img_bipbop_adv_example_fmp4/master.m3u8"}, {"name": "Apple media playlist (TS)", "uri": "https://devstreaming-cdn.apple.com/videos/streaming/examples/bipbop_4x3/gear1/prog_index.m3u8"}, {"name": "Apple media playlist (AAC)", "uri": "https://devstreaming-cdn.apple.com/videos/streaming/examples/bipbop_4x3/gear0/prog_index.m3u8"}]}, {"name": "SmoothStreaming", "samples": [{"name": "Super speed (MP4, H264, Clear)", "uri": "https://playready.directtaps.net/smoothstreaming/SSWSS720H264/SuperSpeedway_720.ism/Manifest"}, {"name": "Super speed (MP4, H264, PlayR<PERSON>y)", "uri": "https://playready.directtaps.net/smoothstreaming/SSWSS720H264PR/SuperSpeedway_720.ism/Manifest", "drm_scheme": "playready", "drm_license_uri": "https://playready.directtaps.net/pr/svc/rightsmanager.asmx", "drm_force_default_license_uri": true}]}, {"name": "IMA sample ad tags", "samples": [{"name": "Single inline linear", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/single_ad_samples&ciu_szs=300x250&impl=s&gdfp_req=1&env=vp&output=vast&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ct%3Dlinear&correlator="}, {"name": "Single skippable inline", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/single_ad_samples&ciu_szs=300x250&impl=s&gdfp_req=1&env=vp&output=vast&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ct%3Dskippablelinear&correlator="}, {"name": "Single redirect linear", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/single_ad_samples&ciu_szs=300x250&impl=s&gdfp_req=1&env=vp&output=vast&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ct%3Dredirectlinear&correlator="}, {"name": "Single redirect error", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/single_ad_samples&ciu_szs=300x250&impl=s&gdfp_req=1&env=vp&output=vast&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ct%3Dredirecterror&nofb=1&correlator="}, {"name": "Single redirect broken (fallback)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/single_ad_samples&ciu_szs=300x250&impl=s&gdfp_req=1&env=vp&output=vast&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ct%3Dredirecterror&correlator="}, {"name": "VMAP pre-roll", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpreonly&cmsid=496&vid=short_onecue&correlator="}, {"name": "VMAP pre-roll + bumper", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpreonlybumper&cmsid=496&vid=short_onecue&correlator="}, {"name": "VMAP post-roll", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpostonly&cmsid=496&vid=short_onecue&correlator="}, {"name": "VMAP post-roll + bumper", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpostonlybumper&cmsid=496&vid=short_onecue&correlator="}, {"name": "VMAP pre-, mid- and post-rolls, single ads", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpremidpost&cmsid=496&vid=short_onecue&correlator="}, {"name": "VMAP pre-roll single ad, mid-roll standard pod with 3 ads, post-roll single ad", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpremidpostpod&cmsid=496&vid=short_onecue&correlator="}, {"name": "VMAP pre-roll single ad, mid-roll optimized pod with 3 ads, post-roll single ad", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpremidpostoptimizedpod&cmsid=496&vid=short_onecue&correlator="}, {"name": "VMAP pre-roll single ad, mid-roll standard pod with 3 ads, post-roll single ad (bumpers around all ad breaks)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpremidpostpodbumper&cmsid=496&vid=short_onecue&correlator="}, {"name": "VMAP pre-roll single ad, mid-roll optimized pod with 3 ads, post-roll single ad (bumpers around all ad breaks)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpremidpostoptimizedpodbumper&cmsid=496&vid=short_onecue&correlator="}, {"name": "VMAP pre-roll single ad, mid-roll standard pods with 5 ads every 10 seconds for 1:40, post-roll single ad", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpremidpostlongpod&cmsid=496&vid=short_tencue&correlator="}, {"name": "VMAP empty midroll", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://vastsynthesizer.appspot.com/empty-midroll"}, {"name": "VMAP full, empty, full midrolls", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://vastsynthesizer.appspot.com/empty-midroll-2"}, {"name": "VMAP midroll at 1765 s", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-one-hour.mp4", "ad_tag_uri": "https://vastsynthesizer.appspot.com/midroll-large"}, {"name": "VMAP midroll ad pod at 5 s with 10 skippable ads", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-one-hour.mp4", "ad_tag_uri": "https://vastsynthesizer.appspot.com/midroll-10-skippable-ads"}, {"name": "Playlist with three ad tags", "playlist": [{"uri": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/android-screens-10s.mp4", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/single_ad_samples&ciu_szs=300x250&impl=s&gdfp_req=1&env=vp&output=vast&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ct%3Dlinear&correlator="}, {"uri": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/android-screens-25s.mp4", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpremidpost&cmsid=496&vid=short_onecue&correlator="}, {"uri": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-one-hour.mp4", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpremidpostlongpod&cmsid=496&vid=short_tencue&correlator="}]}]}, {"name": "IMA DAI streams", "samples": [{"name": "HLS VOD: Demo (skippable pre/post), single ads [30 s]", "uri": "ssai://dai.google.com/?contentSourceId=2483977&videoId=ima-vod-skippable-test&format=2&adsId=1"}, {"name": "HLS VOD: Tears of Steel (pre/mid/mid/mid/post), single ads [10s]", "uri": "ssai://dai.google.com/?contentSourceId=2528370&videoId=tears-of-steel&format=2&adsId=1"}, {"name": "HLS Live: <PERSON> (mid), 3 ads [10/10/10s]", "uri": "ssai://dai.google.com/?assetKey=sN_IYUG8STe1ZzhIIE_ksA&format=2&adsId=3"}, {"name": "DASH VOD: Tears of Steel (11 periods, pre/mid/post), 2/5/2 ads [5/10s]", "uri": "ssai://dai.google.com/?contentSourceId=2559737&videoId=tos-dash&format=0&adsId=1"}, {"name": "DASH live: Tears of Steel (mid), 3 ads each [10 s]", "uri": "ssai://dai.google.com/?assetKey=jNVjPZwzSkyeGiaNQTPqiQ&format=0&adsId=1"}, {"name": "DASH live: New Tears of Steel (mid), 3 ads each [10 s]", "uri": "ssai://dai.google.com/?assetKey=PSzZMzAkSXCmlJOWDmRj8Q&format=0&adsId=12"}, {"name": "DASH live: Unencrypted stream with 30s ad breaks every minute", "uri": "ssai://dai.google.com/?assetKey=0ndl1dJcRmKDUPxTRjvdog&format=0&adsId=21"}, {"name": "Playlist: No ads - HLS VOD: Demo (skippable pre/post) - No ads", "playlist": [{"uri": "https://html5demos.com/assets/dizzy.mp4"}, {"uri": "ssai://dai.google.com/?contentSourceId=2483977&videoId=ima-vod-skippable-test&format=2&adsId=1"}, {"uri": "https://html5demos.com/assets/dizzy.mp4"}]}, {"name": "Playlist: No ads - HLS VOD: Tears of steel (pre/mid/mid/mid/post) - No ads", "playlist": [{"uri": "https://html5demos.com/assets/dizzy.mp4"}, {"uri": "ssai://dai.google.com/?contentSourceId=2528370&videoId=tears-of-steel&format=2&adsId=1"}, {"uri": "https://html5demos.com/assets/dizzy.mp4"}]}, {"name": "Playlist: No ads - DASH VOD: Tears of Steel (11 periods, pre/mid/post) - No ads", "playlist": [{"uri": "https://html5demos.com/assets/dizzy.mp4"}, {"uri": "ssai://dai.google.com/?contentSourceId=2559737&videoId=tos-dash&format=0&adsId=1"}, {"uri": "https://html5demos.com/assets/dizzy.mp4"}]}, {"name": "Playlist: Client-side Ads - DASH VOD: Tears of Steel (11 periods, pre/mid/post) - No ads", "playlist": [{"uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv", "ad_tag_uri": "https://pubads.g.doubleclick.net/gampad/ads?sz=640x480&iu=/124319096/external/ad_rule_samples&ciu_szs=300x250&ad_rule=1&impl=s&gdfp_req=1&env=vp&output=vmap&unviewed_position_start=1&cust_params=deployment%3Ddevsite%26sample_ar%3Dpremidpost&cmsid=496&vid=short_onecue&correlator="}, {"uri": "ssai://dai.google.com/?contentSourceId=2559737&videoId=tos-dash&format=0&adsId=1"}, {"uri": "https://html5demos.com/assets/dizzy.mp4"}]}, {"name": "Playlist: No ads - DASH live: Tears of Steel (mid) - No ads", "playlist": [{"uri": "https://html5demos.com/assets/dizzy.mp4"}, {"uri": "ssai://dai.google.com/?assetKey=PSzZMzAkSXCmlJOWDmRj8Q&format=0&adsId=1"}, {"uri": "https://html5demos.com/assets/dizzy.mp4"}]}, {"name": "Playlist: No ads - HLS live: Big Buck Bunny - No ads", "playlist": [{"uri": "https://html5demos.com/assets/dizzy.mp4"}, {"uri": "ssai://dai.google.com/?assetKey=sN_IYUG8STe1ZzhIIE_ksA&format=2&adsId=3"}, {"uri": "https://html5demos.com/assets/dizzy.mp4"}]}]}, {"name": "Playlists", "samples": [{"name": "Cats -> Dogs", "playlist": [{"uri": "https://html5demos.com/assets/dizzy.mp4"}, {"uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv"}]}, {"name": "Audio -> Video (MKV) -> Video (MKV) -> Audio -> Video (MKV) -> Video (DASH) -> Audio", "playlist": [{"uri": "https://storage.googleapis.com/exoplayer-test-media-1/gen-3/screens/dash-vod-single-segment/audio-141.mp4"}, {"uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv"}, {"uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv"}, {"uri": "https://storage.googleapis.com/exoplayer-test-media-1/gen-3/screens/dash-vod-single-segment/audio-141.mp4"}, {"uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv"}, {"uri": "https://storage.googleapis.com/wvmedia/clear/h264/tears/tears.mpd"}, {"uri": "https://storage.googleapis.com/exoplayer-test-media-1/gen-3/screens/dash-vod-single-segment/audio-141.mp4"}]}, {"name": "Clear -> Enc -> Clear -> Enc -> Enc", "playlist": [{"uri": "https://html5demos.com/assets/dizzy.mp4"}, {"uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears_sd.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?provider=widevine_test"}, {"uri": "https://html5demos.com/assets/dizzy.mp4"}, {"uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears_sd.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?provider=widevine_test"}, {"uri": "https://storage.googleapis.com/wvmedia/cenc/h264/tears/tears_sd.mpd", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?provider=widevine_test"}]}, {"name": "Manual ad insertion", "playlist": [{"uri": "https://html5demos.com/assets/dizzy.mp4", "clip_end_position_ms": 10000}, {"uri": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-one-hour.mp4", "clip_end_position_ms": 5000}, {"uri": "https://html5demos.com/assets/dizzy.mp4", "clip_start_position_ms": 10000}]}, {"name": "Image -> Video -> Image -> Image", "playlist": [{"uri": "https://storage.googleapis.com/exoplayer-test-media-1/jpg/tokyo.jpg", "image_duration_ms": 2000}, {"uri": "https://html5demos.com/assets/dizzy.mp4", "clip_end_position_ms": 2000}, {"uri": "https://storage.googleapis.com/exoplayer-test-media-1/jpg/london.jpg", "image_duration_ms": 2000}, {"uri": "https://storage.googleapis.com/exoplayer-test-media-1/jpg/tokyo.jpg", "image_duration_ms": 2000}]}]}, {"name": "AV1", "samples": [{"name": "SD (WebM, Clear)", "uri": "https://storage.googleapis.com/wvmedia/2019/clear/av1/24/webm/llama_av1_480p_400.webm"}, {"name": "SD (<PERSON><PERSON>, Widevine cenc, L3)", "uri": "https://storage.googleapis.com/wvmedia/2019/cenc/av1/24/webm/llama_av1_480p_400.webm", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_SW_SECURE_CRYPTO&provider=widevine_test"}, {"name": "SD (<PERSON><PERSON>, Widevine cenc, L1)", "uri": "https://storage.googleapis.com/wvmedia/2019/cenc/av1/24/webm/llama_av1_480p_400.webm", "drm_scheme": "widevine", "drm_license_uri": "https://proxy.uat.widevine.com/proxy?video_id=GTS_HW_SECURE_ALL&provider=widevine_test"}]}, {"name": "Subtitles", "samples": [{"name": "TTML positioning", "uri": "https://html5demos.com/assets/dizzy.mp4", "subtitle_uri": "https://storage.googleapis.com/exoplayer-test-media-1/ttml/netflix_ttml_sample.xml", "subtitle_mime_type": "application/ttml+xml", "subtitle_language": "en"}, {"name": "TTML Japanese features", "uri": "https://html5demos.com/assets/dizzy.mp4", "subtitle_uri": "https://storage.googleapis.com/exoplayer-test-media-1/ttml/japanese-ttml.xml", "subtitle_mime_type": "application/ttml+xml", "subtitle_language": "ja"}, {"name": "TTML Netflix Japanese examples (IMSC1.1)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/gen-3/screens/dash-vod-single-segment/video-avc-baseline-480.mp4", "subtitle_uri": "https://storage.googleapis.com/exoplayer-test-media-1/ttml/netflix_japanese_ttml.xml", "subtitle_mime_type": "application/ttml+xml", "subtitle_language": "ja"}, {"name": "WebVTT positioning", "uri": "https://html5demos.com/assets/dizzy.mp4", "subtitle_uri": "https://storage.googleapis.com/exoplayer-test-media-1/webvtt/numeric-lines.vtt", "subtitle_mime_type": "text/vtt", "subtitle_language": "en"}, {"name": "WebVTT Japanese features", "uri": "https://html5demos.com/assets/dizzy.mp4", "subtitle_uri": "https://storage.googleapis.com/exoplayer-test-media-1/webvtt/japanese.vtt", "subtitle_mime_type": "text/vtt", "subtitle_language": "ja"}, {"name": "SubStation Alpha positioning", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/gen-3/screens/dash-vod-single-segment/video-avc-baseline-480.mp4", "subtitle_uri": "https://storage.googleapis.com/exoplayer-test-media-1/ssa/test-subs-position.ass", "subtitle_mime_type": "text/x-ssa", "subtitle_language": "en"}, {"name": "SubStation Alpha styling", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/gen-3/screens/dash-vod-single-segment/video-avc-baseline-480.mp4", "subtitle_uri": "https://storage.googleapis.com/exoplayer-test-media-1/ssa/test-subs-styling.ass", "subtitle_mime_type": "text/x-ssa", "subtitle_language": "en"}, {"name": "MPEG-4 Timed Text", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/dizzy-with-tx3g.mp4"}, {"name": "SubRip muxed into MKV", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-with-subrip.mkv"}, {"name": "Overlapping SSA muxed into MKV", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-with-overlapping-ssa.mkv"}]}, {"name": "Progressive", "samples": [{"name": "<PERSON><PERSON> (MP4)", "uri": "https://html5demos.com/assets/dizzy.mp4"}, {"name": "Apple 10s (AAC)", "uri": "https://devstreaming-cdn.apple.com/videos/streaming/examples/bipbop_4x3/gear0/fileSequence0.aac"}, {"name": "Apple 10s (TS)", "uri": "https://devstreaming-cdn.apple.com/videos/streaming/examples/bipbop_4x3/gear1/fileSequence0.ts"}, {"name": "Android screens (MKV)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mkv/android-screens-lavf-56.36.100-aac-avc-main-1280x720.mkv"}, {"name": "Screens 360p (WebM, VP9)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/gen-3/screens/dash-vod-single-segment/video-vp9-360.webm"}, {"name": "Screens 480p (FMP4, H264)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/gen-3/screens/dash-vod-single-segment/video-avc-baseline-480.mp4"}, {"name": "Screens 1080p (FMP4, H264)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/gen-3/screens/dash-vod-single-segment/video-137.mp4"}, {"name": "Screens audio (FMP4)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/gen-3/screens/dash-vod-single-segment/audio-141.mp4"}, {"name": "Google Play (MP3)", "uri": "https://storage.googleapis.com/exoplayer-test-media-0/play.mp3"}, {"name": "Google Play (Ogg, Vorbis)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/ogg/play.ogg"}, {"name": "Google Play (Flac)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/flac/play.flac"}, {"name": "Big Buck Bunny 480p (MP4, AV1)", "uri": "https://storage.googleapis.com/downloads.webmproject.org/av1/exoplayer/bbb-av1-480p.mp4"}, {"name": "One hour frame counter (MP4)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/mp4/frame-counter-one-hour.mp4"}, {"name": "Immersive Audio Format Sample (MP4, IAMF)", "uri": "https://github.com/AOMediaCodec/libiamf/raw/main/tests/test_000036_s.mp4"}]}, {"name": "Images", "samples": [{"name": "JPEG (wide)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/jpg/london.jpg", "image_duration_ms": 2000}, {"name": "JPEG (tall)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/jpg/tokyo.jpg", "image_duration_ms": 2000}, {"name": "PNG", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/png/media3test.png", "image_duration_ms": 2000}, {"name": "JPEG motion photo (still)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/jpg/london_motion_photo.jpg", "image_duration_ms": 2000}, {"name": "JPEG motion photo (motion)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/jpg/london_motion_photo.jpg"}, {"name": "JPEG (Ultra HDR)", "uri": "https://storage.googleapis.com/exoplayer-test-media-1/jpg/ultra_hdr.jpg", "image_duration_ms": 2000}]}]