/*
 * Copyright 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
apply from: '../../constants.gradle'
apply plugin: 'com.android.application'

android {
    namespace 'androidx.media3.demo.composition'

    compileSdk project.ext.compileSdkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        versionName project.ext.releaseVersion
        versionCode project.ext.releaseVersionCode
        minSdkVersion project.ext.minSdkVersion
        targetSdkVersion project.ext.appTargetSdkVersion
    }

    buildTypes {
        release {
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.txt'
            signingConfig signingConfigs.debug
        }
    }

    lintOptions {
        // This demo app isn't indexed and doesn't have translations.
        disable 'GoogleAppIndexingWarning','MissingTranslation'
    }
}

dependencies {
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:' + androidxMaterialVersion
    implementation project(modulePrefix + 'lib-effect')
    implementation project(modulePrefix + 'lib-exoplayer')
    implementation project(modulePrefix + 'lib-exoplayer-dash')
    implementation project(modulePrefix + 'lib-muxer')
    implementation project(modulePrefix + 'lib-transformer')
    implementation project(modulePrefix + 'lib-ui')
    implementation 'androidx.annotation:annotation:' + androidxAnnotationVersion
    compileOnly 'org.checkerframework:checker-qual:' + checkerframeworkVersion
}
