<?xml version="1.0" encoding="utf-8"?>
<android.widget.RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@android:id/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/ui_widget_bg_sharp"
    android:backgroundTint="?attr/colorSurface"
    android:theme="@style/Theme.Auxio.Widget">

    <!--
    See widget_small.xml for an explanation for the ImageView setup.
    -->

    <android.widget.ImageView
        android:id="@+id/widget_aspect_ratio"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/widget_panel"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="@dimen/spacing_medium"
        android:layout_marginTop="@dimen/spacing_medium"
        android:layout_marginEnd="@dimen/spacing_medium"
        android:adjustViewBounds="true"
        android:scaleType="fitCenter"
        android:src="@drawable/ui_remote_aspect_ratio"
        android:visibility="invisible"
        tools:ignore="ContentDescription" />

    <android.widget.ImageView
        android:id="@+id/widget_cover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_alignStart="@id/widget_aspect_ratio"
        android:layout_alignTop="@id/widget_aspect_ratio"
        android:layout_alignEnd="@id/widget_aspect_ratio"
        android:layout_alignBottom="@id/widget_aspect_ratio"
        android:src="@drawable/ic_remote_default_cover_24"
        tools:ignore="ContentDescription" />

    <android.widget.LinearLayout
        android:id="@+id/widget_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:paddingTop="@dimen/spacing_medium">

        <android.widget.TextView
            android:id="@+id/widget_song"
            style="@style/Widget.Auxio.TextView.Primary.AppWidget"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/spacing_medium"
            android:layout_marginEnd="@dimen/spacing_medium"
            tools:text="Song name" />

        <android.widget.TextView
            android:id="@+id/widget_artist"
            style="@style/Widget.Auxio.TextView.Secondary.AppWidget"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/spacing_medium"
            android:layout_marginEnd="@dimen/spacing_medium"
            tools:text="Artist name" />

        <android.widget.LinearLayout
            android:id="@+id/widget_controls"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingStart="@dimen/spacing_mid_medium"
            android:paddingTop="@dimen/spacing_medium"
            android:paddingEnd="@dimen/spacing_mid_medium"
            android:paddingBottom="@dimen/spacing_medium">

            <android.widget.ImageButton
                android:id="@+id/widget_skip_prev"
                style="@style/Widget.Auxio.MaterialButton.AppWidget"
                android:layout_width="@dimen/size_touchable_small"
                android:layout_height="@dimen/size_touchable_small"
                android:contentDescription="@string/desc_skip_prev"
                android:src="@drawable/ic_skip_prev_24" />

            <android.widget.ImageView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <android.widget.ImageButton
                android:id="@+id/widget_play_pause"
                style="@style/Widget.Auxio.MaterialButton.AppWidget.PlayPause"
                android:layout_width="@dimen/size_touchable_small"
                android:layout_height="@dimen/size_touchable_small"
                android:contentDescription="@string/desc_play_pause"
                android:src="@drawable/ic_play_24" />

            <android.widget.ImageView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <android.widget.ImageButton
                android:id="@+id/widget_skip_next"
                style="@style/Widget.Auxio.MaterialButton.AppWidget"
                android:layout_width="@dimen/size_touchable_small"
                android:layout_height="@dimen/size_touchable_small"
                android:contentDescription="@string/desc_skip_next"
                android:src="@drawable/ic_skip_next_24" />

        </android.widget.LinearLayout>

    </android.widget.LinearLayout>
</android.widget.RelativeLayout>


