/*
 * Copyright (c) 2023 Auxio Project
 * PersistenceModule.kt is part of Auxio.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
 
package org.oxycblt.auxio.playback.persist

import android.content.Context
import androidx.room.Room
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
interface PersistenceModule {
    @Binds fun repository(persistenceRepository: PersistenceRepositoryImpl): PersistenceRepository
}

@Module
@InstallIn(SingletonComponent::class)
class PersistenceRoomModule {
    @Singleton
    @Provides
    fun database(@ApplicationContext context: Context) =
        Room.databaseBuilder(
                context.applicationContext,
                PersistenceDatabase::class.java,
                "playback_persistence.db")
            .fallbackToDestructiveMigration()
            .addMigrations(PersistenceDatabase.MIGRATION_27_32)
            .build()

    @Provides fun playbackStateDao(database: PersistenceDatabase) = database.playbackStateDao()

    @Provides fun queueDao(database: PersistenceDatabase) = database.queueDao()
}
