<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2023 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:orientation="vertical"
  tools:context=".MainActivity">

  <Button android:id="@+id/view_pager_button"
    android:text="@string/open_view_pager_activity"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="12dp"
    android:layout_marginStart="12dp"
    android:layout_marginEnd="12dp"
    android:layout_marginRight="12dp"/>

  <com.google.android.material.textfield.TextInputEditText
    android:id="@+id/num_players_field"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    android:layout_marginTop="150dp"
    android:layout_marginStart="12dp"
    android:layout_marginEnd="12dp"
    android:layout_marginLeft="12dp"
    android:layout_marginRight="12dp"
    android:layout_gravity="center_horizontal"
    android:background="@color/purple_700"
    android:gravity="center"
    android:hint="@string/num_of_players"
    android:inputType="number"
    android:textColorHint="@color/grey" />

</LinearLayout>
