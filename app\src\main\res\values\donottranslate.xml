<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Info namespace | App labels -->
    <string name="info_app_name" translatable="false">Auxio</string>

    <!-- Format Namespace | Value formatting/plurals -->
    <string name="fmt_two" translatable="false">%1$s • %2$s</string>
    <string name="fmt_three" translatable="false">%1$s • %2$s • %3$s</string>
    <string name="fmt_number" translatable="false">%d</string>
    <string name="fmt_zipped_names" translatable="false">%1$s (%2$s)</string>
    <string name="fmt_date_range" translatable="false">%s - %s</string>

    <!-- Codec Namespace | Format names -->
    <string name="cdc_vorbis">Vorbis</string>
    <string name="cdc_opus">Opus</string>
    <string name="cdc_wav">Microsoft WAVE</string>
    <string name="cnt_ogg">Ogg %s</string>

    <!-- Pkg Namespace | Dynamic package names -->
    <string name="pkg_authority_cover">org.oxycblt.auxio.image.CoverProvider</string>

    <!-- Supporter Namespace | Sponsor usernames  -->
    <string name="sup_mark_pitblado">Mark Pitblado</string>
</resources>