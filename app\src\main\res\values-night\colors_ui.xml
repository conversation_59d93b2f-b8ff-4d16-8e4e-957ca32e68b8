<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="red_primary">#FFB4A9</color>
    <color name="red_onPrimary">#561E18</color>
    <color name="red_primaryContainer">#73342C</color>
    <color name="red_onPrimaryContainer">#FFDAD5</color>
    <color name="red_secondary">#E7BDB7</color>
    <color name="red_onSecondary">#442926</color>
    <color name="red_secondaryContainer">#5D3F3B</color>
    <color name="red_onSecondaryContainer">#FFDAD5</color>
    <color name="red_tertiary">#DFC38C</color>
    <color name="red_onTertiary">#3E2E04</color>
    <color name="red_tertiaryContainer">#574419</color>
    <color name="red_onTertiaryContainer">#FCDFA6</color>
    <color name="red_error">#FFB4AB</color>
    <color name="red_onError">#690005</color>
    <color name="red_errorContainer">#93000A</color>
    <color name="red_onErrorContainer">#FFDAD6</color>
    <color name="red_background">#1A1110</color>
    <color name="red_onBackground">#F1DEDC</color>
    <color name="red_surface">#1A1110</color>
    <color name="red_onSurface">#F1DEDC</color>
    <color name="red_surfaceVariant">#534341</color>
    <color name="red_onSurfaceVariant">#D8C2BE</color>
    <color name="red_outline">#A08C89</color>
    <color name="red_outlineVariant">#534341</color>
    <color name="red_inverseSurface">#F1DEDC</color>
    <color name="red_inverseOnSurface">#392E2C</color>
    <color name="red_inversePrimary">#904A42</color>
    <color name="red_primaryFixed">#FFDAD5</color>
    <color name="red_onPrimaryFixed">#3B0906</color>
    <color name="red_primaryFixedDim">#FFB4A9</color>
    <color name="red_onPrimaryFixedVariant">#73342C</color>
    <color name="red_secondaryFixed">#FFDAD5</color>
    <color name="red_onSecondaryFixed">#2C1512</color>
    <color name="red_secondaryFixedDim">#E7BDB7</color>
    <color name="red_onSecondaryFixedVariant">#5D3F3B</color>
    <color name="red_tertiaryFixed">#FCDFA6</color>
    <color name="red_onTertiaryFixed">#261A00</color>
    <color name="red_tertiaryFixedDim">#DFC38C</color>
    <color name="red_onTertiaryFixedVariant">#574419</color>
    <color name="red_surfaceDim">#1A1110</color>
    <color name="red_surfaceBright">#423735</color>
    <color name="red_surfaceContainerLowest">#140C0B</color>
    <color name="red_surfaceContainerLow">#231918</color>
    <color name="red_surfaceContainer">#271D1C</color>
    <color name="red_surfaceContainerHigh">#322826</color>
    <color name="red_surfaceContainerHighest">#3D3231</color>
    <color name="pink_primary">#FFB2BD</color>
    <color name="pink_onPrimary">#561D29</color>
    <color name="pink_primaryContainer">#72333F</color>
    <color name="pink_onPrimaryContainer">#FFD9DD</color>
    <color name="pink_secondary">#E5BDC1</color>
    <color name="pink_onSecondary">#43292D</color>
    <color name="pink_secondaryContainer">#5C3F43</color>
    <color name="pink_onSecondaryContainer">#FFD9DD</color>
    <color name="pink_tertiary">#EABF8F</color>
    <color name="pink_onTertiary">#452B07</color>
    <color name="pink_tertiaryContainer">#5E411C</color>
    <color name="pink_onTertiaryContainer">#FFDDB9</color>
    <color name="pink_error">#FFB4AB</color>
    <color name="pink_onError">#690005</color>
    <color name="pink_errorContainer">#93000A</color>
    <color name="pink_onErrorContainer">#FFDAD6</color>
    <color name="pink_background">#191112</color>
    <color name="pink_onBackground">#F0DEDF</color>
    <color name="pink_surface">#191112</color>
    <color name="pink_onSurface">#F0DEDF</color>
    <color name="pink_surfaceVariant">#524345</color>
    <color name="pink_onSurfaceVariant">#D6C2C3</color>
    <color name="pink_outline">#9F8C8E</color>
    <color name="pink_outlineVariant">#524345</color>
    <color name="pink_inverseSurface">#F0DEDF</color>
    <color name="pink_inverseOnSurface">#382E2F</color>
    <color name="pink_inversePrimary">#8E4956</color>
    <color name="pink_primaryFixed">#FFD9DD</color>
    <color name="pink_onPrimaryFixed">#3B0715</color>
    <color name="pink_primaryFixedDim">#FFB2BD</color>
    <color name="pink_onPrimaryFixedVariant">#72333F</color>
    <color name="pink_secondaryFixed">#FFD9DD</color>
    <color name="pink_onSecondaryFixed">#2C1519</color>
    <color name="pink_secondaryFixedDim">#E5BDC1</color>
    <color name="pink_onSecondaryFixedVariant">#5C3F43</color>
    <color name="pink_tertiaryFixed">#FFDDB9</color>
    <color name="pink_onTertiaryFixed">#2B1700</color>
    <color name="pink_tertiaryFixedDim">#EABF8F</color>
    <color name="pink_onTertiaryFixedVariant">#5E411C</color>
    <color name="pink_surfaceDim">#191112</color>
    <color name="pink_surfaceBright">#413738</color>
    <color name="pink_surfaceContainerLowest">#140C0D</color>
    <color name="pink_surfaceContainerLow">#22191A</color>
    <color name="pink_surfaceContainer">#261D1E</color>
    <color name="pink_surfaceContainerHigh">#312829</color>
    <color name="pink_surfaceContainerHighest">#3D3233</color>
    <color name="purple_primary">#EBB5ED</color>
    <color name="purple_onPrimary">#49204E</color>
    <color name="purple_primaryContainer">#613766</color>
    <color name="purple_onPrimaryContainer">#FFD6FE</color>
    <color name="purple_secondary">#D7BFD5</color>
    <color name="purple_onSecondary">#3B2B3C</color>
    <color name="purple_secondaryContainer">#534153</color>
    <color name="purple_onSecondaryContainer">#F4DBF1</color>
    <color name="purple_tertiary">#F6B8AD</color>
    <color name="purple_onTertiary">#4C251F</color>
    <color name="purple_tertiaryContainer">#673B34</color>
    <color name="purple_onTertiaryContainer">#FFDAD4</color>
    <color name="purple_error">#FFB4AB</color>
    <color name="purple_onError">#690005</color>
    <color name="purple_errorContainer">#93000A</color>
    <color name="purple_onErrorContainer">#FFDAD6</color>
    <color name="purple_background">#171216</color>
    <color name="purple_onBackground">#EBDFE6</color>
    <color name="purple_surface">#171216</color>
    <color name="purple_onSurface">#EBDFE6</color>
    <color name="purple_surfaceVariant">#4D444C</color>
    <color name="purple_onSurfaceVariant">#D0C3CC</color>
    <color name="purple_outline">#998D96</color>
    <color name="purple_outlineVariant">#4D444C</color>
    <color name="purple_inverseSurface">#EBDFE6</color>
    <color name="purple_inverseOnSurface">#352F34</color>
    <color name="purple_inversePrimary">#7B4E7F</color>
    <color name="purple_primaryFixed">#FFD6FE</color>
    <color name="purple_onPrimaryFixed">#310937</color>
    <color name="purple_primaryFixedDim">#EBB5ED</color>
    <color name="purple_onPrimaryFixedVariant">#613766</color>
    <color name="purple_secondaryFixed">#F4DBF1</color>
    <color name="purple_onSecondaryFixed">#251626</color>
    <color name="purple_secondaryFixedDim">#D7BFD5</color>
    <color name="purple_onSecondaryFixedVariant">#534153</color>
    <color name="purple_tertiaryFixed">#FFDAD4</color>
    <color name="purple_onTertiaryFixed">#33110C</color>
    <color name="purple_tertiaryFixedDim">#F6B8AD</color>
    <color name="purple_onTertiaryFixedVariant">#673B34</color>
    <color name="purple_surfaceDim">#171216</color>
    <color name="purple_surfaceBright">#3E373D</color>
    <color name="purple_surfaceContainerLowest">#110D11</color>
    <color name="purple_surfaceContainerLow">#1F1A1F</color>
    <color name="purple_surfaceContainer">#231E23</color>
    <color name="purple_surfaceContainerHigh">#2E282D</color>
    <color name="purple_surfaceContainerHighest">#393338</color>
    <color name="deep_purple_primary">#D3BCFD</color>
    <color name="deep_purple_onPrimary">#38265C</color>
    <color name="deep_purple_primaryContainer">#4F3D74</color>
    <color name="deep_purple_onPrimaryContainer">#EBDDFF</color>
    <color name="deep_purple_secondary">#CDC2DB</color>
    <color name="deep_purple_onSecondary">#342D40</color>
    <color name="deep_purple_secondaryContainer">#4B4358</color>
    <color name="deep_purple_onSecondaryContainer">#E9DEF8</color>
    <color name="deep_purple_tertiary">#F0B7C5</color>
    <color name="deep_purple_onTertiary">#4A2530</color>
    <color name="deep_purple_tertiaryContainer">#643B46</color>
    <color name="deep_purple_onTertiaryContainer">#FFD9E1</color>
    <color name="deep_purple_error">#FFB4AB</color>
    <color name="deep_purple_onError">#690005</color>
    <color name="deep_purple_errorContainer">#93000A</color>
    <color name="deep_purple_onErrorContainer">#FFDAD6</color>
    <color name="deep_purple_background">#151218</color>
    <color name="deep_purple_onBackground">#E7E0E8</color>
    <color name="deep_purple_surface">#151218</color>
    <color name="deep_purple_onSurface">#E7E0E8</color>
    <color name="deep_purple_surfaceVariant">#49454E</color>
    <color name="deep_purple_onSurfaceVariant">#CBC4CF</color>
    <color name="deep_purple_outline">#948F99</color>
    <color name="deep_purple_outlineVariant">#49454E</color>
    <color name="deep_purple_inverseSurface">#E7E0E8</color>
    <color name="deep_purple_inverseOnSurface">#322F35</color>
    <color name="deep_purple_inversePrimary">#68548E</color>
    <color name="deep_purple_primaryFixed">#EBDDFF</color>
    <color name="deep_purple_onPrimaryFixed">#230F46</color>
    <color name="deep_purple_primaryFixedDim">#D3BCFD</color>
    <color name="deep_purple_onPrimaryFixedVariant">#4F3D74</color>
    <color name="deep_purple_secondaryFixed">#E9DEF8</color>
    <color name="deep_purple_onSecondaryFixed">#1F182B</color>
    <color name="deep_purple_secondaryFixedDim">#CDC2DB</color>
    <color name="deep_purple_onSecondaryFixedVariant">#4B4358</color>
    <color name="deep_purple_tertiaryFixed">#FFD9E1</color>
    <color name="deep_purple_onTertiaryFixed">#31101B</color>
    <color name="deep_purple_tertiaryFixedDim">#F0B7C5</color>
    <color name="deep_purple_onTertiaryFixedVariant">#643B46</color>
    <color name="deep_purple_surfaceDim">#151218</color>
    <color name="deep_purple_surfaceBright">#3B383E</color>
    <color name="deep_purple_surfaceContainerLowest">#0F0D13</color>
    <color name="deep_purple_surfaceContainerLow">#1D1B20</color>
    <color name="deep_purple_surfaceContainer">#211F24</color>
    <color name="deep_purple_surfaceContainerHigh">#2C292F</color>
    <color name="deep_purple_surfaceContainerHighest">#36343A</color>
    <color name="indigo_primary">#BAC3FF</color>
    <color name="indigo_onPrimary">#222C61</color>
    <color name="indigo_primaryContainer">#394379</color>
    <color name="indigo_onPrimaryContainer">#DEE0FF</color>
    <color name="indigo_secondary">#C3C5DD</color>
    <color name="indigo_onSecondary">#2D2F42</color>
    <color name="indigo_secondaryContainer">#434659</color>
    <color name="indigo_onSecondaryContainer">#E0E1F9</color>
    <color name="indigo_tertiary">#E6BAD7</color>
    <color name="indigo_onTertiary">#44263D</color>
    <color name="indigo_tertiaryContainer">#5D3C55</color>
    <color name="indigo_onTertiaryContainer">#FFD7F1</color>
    <color name="indigo_error">#FFB4AB</color>
    <color name="indigo_onError">#690005</color>
    <color name="indigo_errorContainer">#93000A</color>
    <color name="indigo_onErrorContainer">#FFDAD6</color>
    <color name="indigo_background">#121318</color>
    <color name="indigo_onBackground">#E4E1E9</color>
    <color name="indigo_surface">#121318</color>
    <color name="indigo_onSurface">#E4E1E9</color>
    <color name="indigo_surfaceVariant">#46464F</color>
    <color name="indigo_onSurfaceVariant">#C7C5D0</color>
    <color name="indigo_outline">#90909A</color>
    <color name="indigo_outlineVariant">#46464F</color>
    <color name="indigo_inverseSurface">#E4E1E9</color>
    <color name="indigo_inverseOnSurface">#303036</color>
    <color name="indigo_inversePrimary">#515B92</color>
    <color name="indigo_primaryFixed">#DEE0FF</color>
    <color name="indigo_onPrimaryFixed">#0B154B</color>
    <color name="indigo_primaryFixedDim">#BAC3FF</color>
    <color name="indigo_onPrimaryFixedVariant">#394379</color>
    <color name="indigo_secondaryFixed">#E0E1F9</color>
    <color name="indigo_onSecondaryFixed">#181A2C</color>
    <color name="indigo_secondaryFixedDim">#C3C5DD</color>
    <color name="indigo_onSecondaryFixedVariant">#434659</color>
    <color name="indigo_tertiaryFixed">#FFD7F1</color>
    <color name="indigo_onTertiaryFixed">#2D1228</color>
    <color name="indigo_tertiaryFixedDim">#E6BAD7</color>
    <color name="indigo_onTertiaryFixedVariant">#5D3C55</color>
    <color name="indigo_surfaceDim">#121318</color>
    <color name="indigo_surfaceBright">#39393F</color>
    <color name="indigo_surfaceContainerLowest">#0D0E13</color>
    <color name="indigo_surfaceContainerLow">#1B1B21</color>
    <color name="indigo_surfaceContainer">#1F1F25</color>
    <color name="indigo_surfaceContainerHigh">#29292F</color>
    <color name="indigo_surfaceContainerHighest">#34343A</color>
    <color name="blue_primary">#A1C9FD</color>
    <color name="blue_onPrimary">#003259</color>
    <color name="blue_primaryContainer">#1B4975</color>
    <color name="blue_onPrimaryContainer">#D2E4FF</color>
    <color name="blue_secondary">#BBC7DB</color>
    <color name="blue_onSecondary">#253141</color>
    <color name="blue_secondaryContainer">#3C4858</color>
    <color name="blue_onSecondaryContainer">#D7E3F8</color>
    <color name="blue_tertiary">#D7BDE4</color>
    <color name="blue_onTertiary">#3B2947</color>
    <color name="blue_tertiaryContainer">#533F5F</color>
    <color name="blue_onTertiaryContainer">#F3DAFF</color>
    <color name="blue_error">#FFB4AB</color>
    <color name="blue_onError">#690005</color>
    <color name="blue_errorContainer">#93000A</color>
    <color name="blue_onErrorContainer">#FFDAD6</color>
    <color name="blue_background">#111418</color>
    <color name="blue_onBackground">#E1E2E8</color>
    <color name="blue_surface">#111418</color>
    <color name="blue_onSurface">#E1E2E8</color>
    <color name="blue_surfaceVariant">#43474E</color>
    <color name="blue_onSurfaceVariant">#C3C6CF</color>
    <color name="blue_outline">#8D9199</color>
    <color name="blue_outlineVariant">#43474E</color>
    <color name="blue_inverseSurface">#E1E2E8</color>
    <color name="blue_inverseOnSurface">#2E3135</color>
    <color name="blue_inversePrimary">#37618E</color>
    <color name="blue_primaryFixed">#D2E4FF</color>
    <color name="blue_onPrimaryFixed">#001C37</color>
    <color name="blue_primaryFixedDim">#A1C9FD</color>
    <color name="blue_onPrimaryFixedVariant">#1B4975</color>
    <color name="blue_secondaryFixed">#D7E3F8</color>
    <color name="blue_onSecondaryFixed">#101C2B</color>
    <color name="blue_secondaryFixedDim">#BBC7DB</color>
    <color name="blue_onSecondaryFixedVariant">#3C4858</color>
    <color name="blue_tertiaryFixed">#F3DAFF</color>
    <color name="blue_onTertiaryFixed">#251431</color>
    <color name="blue_tertiaryFixedDim">#D7BDE4</color>
    <color name="blue_onTertiaryFixedVariant">#533F5F</color>
    <color name="blue_surfaceDim">#111418</color>
    <color name="blue_surfaceBright">#36393E</color>
    <color name="blue_surfaceContainerLowest">#0B0E13</color>
    <color name="blue_surfaceContainerLow">#191C20</color>
    <color name="blue_surfaceContainer">#1D2024</color>
    <color name="blue_surfaceContainerHigh">#272A2F</color>
    <color name="blue_surfaceContainerHighest">#32353A</color>
    <color name="deep_blue_primary">#8BD0F0</color>
    <color name="deep_blue_onPrimary">#003546</color>
    <color name="deep_blue_primaryContainer">#004D64</color>
    <color name="deep_blue_onPrimaryContainer">#BEE9FF</color>
    <color name="deep_blue_secondary">#B4CAD6</color>
    <color name="deep_blue_onSecondary">#1F333D</color>
    <color name="deep_blue_secondaryContainer">#354A54</color>
    <color name="deep_blue_onSecondaryContainer">#D0E6F2</color>
    <color name="deep_blue_tertiary">#C6C2EA</color>
    <color name="deep_blue_onTertiary">#2F2D4D</color>
    <color name="deep_blue_tertiaryContainer">#454364</color>
    <color name="deep_blue_onTertiaryContainer">#E3DFFF</color>
    <color name="deep_blue_error">#FFB4AB</color>
    <color name="deep_blue_onError">#690005</color>
    <color name="deep_blue_errorContainer">#93000A</color>
    <color name="deep_blue_onErrorContainer">#FFDAD6</color>
    <color name="deep_blue_background">#0F1417</color>
    <color name="deep_blue_onBackground">#DFE3E7</color>
    <color name="deep_blue_surface">#0F1417</color>
    <color name="deep_blue_onSurface">#DFE3E7</color>
    <color name="deep_blue_surfaceVariant">#40484C</color>
    <color name="deep_blue_onSurfaceVariant">#C0C8CD</color>
    <color name="deep_blue_outline">#8A9297</color>
    <color name="deep_blue_outlineVariant">#40484C</color>
    <color name="deep_blue_inverseSurface">#DFE3E7</color>
    <color name="deep_blue_inverseOnSurface">#2C3134</color>
    <color name="deep_blue_inversePrimary">#136682</color>
    <color name="deep_blue_primaryFixed">#BEE9FF</color>
    <color name="deep_blue_onPrimaryFixed">#001F2A</color>
    <color name="deep_blue_primaryFixedDim">#8BD0F0</color>
    <color name="deep_blue_onPrimaryFixedVariant">#004D64</color>
    <color name="deep_blue_secondaryFixed">#D0E6F2</color>
    <color name="deep_blue_onSecondaryFixed">#081E27</color>
    <color name="deep_blue_secondaryFixedDim">#B4CAD6</color>
    <color name="deep_blue_onSecondaryFixedVariant">#354A54</color>
    <color name="deep_blue_tertiaryFixed">#E3DFFF</color>
    <color name="deep_blue_onTertiaryFixed">#1A1836</color>
    <color name="deep_blue_tertiaryFixedDim">#C6C2EA</color>
    <color name="deep_blue_onTertiaryFixedVariant">#454364</color>
    <color name="deep_blue_surfaceDim">#0F1417</color>
    <color name="deep_blue_surfaceBright">#353A3D</color>
    <color name="deep_blue_surfaceContainerLowest">#0A0F11</color>
    <color name="deep_blue_surfaceContainerLow">#171C1F</color>
    <color name="deep_blue_surfaceContainer">#1B2023</color>
    <color name="deep_blue_surfaceContainerHigh">#262B2E</color>
    <color name="deep_blue_surfaceContainerHighest">#303538</color>
    <color name="cyan_primary">#83D2E4</color>
    <color name="cyan_onPrimary">#00363F</color>
    <color name="cyan_primaryContainer">#004E5A</color>
    <color name="cyan_onPrimaryContainer">#A4EEFF</color>
    <color name="cyan_secondary">#B2CBD1</color>
    <color name="cyan_onSecondary">#1C3439</color>
    <color name="cyan_secondaryContainer">#334A50</color>
    <color name="cyan_onSecondaryContainer">#CDE7ED</color>
    <color name="cyan_tertiary">#BDC5EB</color>
    <color name="cyan_onTertiary">#262F4D</color>
    <color name="cyan_tertiaryContainer">#3D4565</color>
    <color name="cyan_onTertiaryContainer">#DCE1FF</color>
    <color name="cyan_error">#FFB4AB</color>
    <color name="cyan_onError">#690005</color>
    <color name="cyan_errorContainer">#93000A</color>
    <color name="cyan_onErrorContainer">#FFDAD6</color>
    <color name="cyan_background">#0E1416</color>
    <color name="cyan_onBackground">#DEE3E5</color>
    <color name="cyan_surface">#0E1416</color>
    <color name="cyan_onSurface">#DEE3E5</color>
    <color name="cyan_surfaceVariant">#3F484B</color>
    <color name="cyan_onSurfaceVariant">#BFC8CB</color>
    <color name="cyan_outline">#899295</color>
    <color name="cyan_outlineVariant">#3F484B</color>
    <color name="cyan_inverseSurface">#DEE3E5</color>
    <color name="cyan_inverseOnSurface">#2B3133</color>
    <color name="cyan_inversePrimary">#006877</color>
    <color name="cyan_primaryFixed">#A4EEFF</color>
    <color name="cyan_onPrimaryFixed">#001F25</color>
    <color name="cyan_primaryFixedDim">#83D2E4</color>
    <color name="cyan_onPrimaryFixedVariant">#004E5A</color>
    <color name="cyan_secondaryFixed">#CDE7ED</color>
    <color name="cyan_onSecondaryFixed">#051F24</color>
    <color name="cyan_secondaryFixedDim">#B2CBD1</color>
    <color name="cyan_onSecondaryFixedVariant">#334A50</color>
    <color name="cyan_tertiaryFixed">#DCE1FF</color>
    <color name="cyan_onTertiaryFixed">#111A37</color>
    <color name="cyan_tertiaryFixedDim">#BDC5EB</color>
    <color name="cyan_onTertiaryFixedVariant">#3D4565</color>
    <color name="cyan_surfaceDim">#0E1416</color>
    <color name="cyan_surfaceBright">#343A3C</color>
    <color name="cyan_surfaceContainerLowest">#090F11</color>
    <color name="cyan_surfaceContainerLow">#171D1E</color>
    <color name="cyan_surfaceContainer">#1B2122</color>
    <color name="cyan_surfaceContainerHigh">#252B2C</color>
    <color name="cyan_surfaceContainerHighest">#303637</color>
    <color name="teal_primary">#82D5C7</color>
    <color name="teal_onPrimary">#003731</color>
    <color name="teal_primaryContainer">#005048</color>
    <color name="teal_onPrimaryContainer">#9EF2E3</color>
    <color name="teal_secondary">#B1CCC6</color>
    <color name="teal_onSecondary">#1C3531</color>
    <color name="teal_secondaryContainer">#334B47</color>
    <color name="teal_onSecondaryContainer">#CCE8E2</color>
    <color name="teal_tertiary">#ADCAE5</color>
    <color name="teal_onTertiary">#143349</color>
    <color name="teal_tertiaryContainer">#2D4960</color>
    <color name="teal_onTertiaryContainer">#CCE5FF</color>
    <color name="teal_error">#FFB4AB</color>
    <color name="teal_onError">#690005</color>
    <color name="teal_errorContainer">#93000A</color>
    <color name="teal_onErrorContainer">#FFDAD6</color>
    <color name="teal_background">#0E1513</color>
    <color name="teal_onBackground">#DDE4E1</color>
    <color name="teal_surface">#0E1513</color>
    <color name="teal_onSurface">#DDE4E1</color>
    <color name="teal_surfaceVariant">#3F4946</color>
    <color name="teal_onSurfaceVariant">#BEC9C5</color>
    <color name="teal_outline">#899390</color>
    <color name="teal_outlineVariant">#3F4946</color>
    <color name="teal_inverseSurface">#DDE4E1</color>
    <color name="teal_inverseOnSurface">#2B3230</color>
    <color name="teal_inversePrimary">#006B5F</color>
    <color name="teal_primaryFixed">#9EF2E3</color>
    <color name="teal_onPrimaryFixed">#00201C</color>
    <color name="teal_primaryFixedDim">#82D5C7</color>
    <color name="teal_onPrimaryFixedVariant">#005048</color>
    <color name="teal_secondaryFixed">#CCE8E2</color>
    <color name="teal_onSecondaryFixed">#06201C</color>
    <color name="teal_secondaryFixedDim">#B1CCC6</color>
    <color name="teal_onSecondaryFixedVariant">#334B47</color>
    <color name="teal_tertiaryFixed">#CCE5FF</color>
    <color name="teal_onTertiaryFixed">#001E31</color>
    <color name="teal_tertiaryFixedDim">#ADCAE5</color>
    <color name="teal_onTertiaryFixedVariant">#2D4960</color>
    <color name="teal_surfaceDim">#0E1513</color>
    <color name="teal_surfaceBright">#343B39</color>
    <color name="teal_surfaceContainerLowest">#090F0E</color>
    <color name="teal_surfaceContainerLow">#161D1B</color>
    <color name="teal_surfaceContainer">#1A211F</color>
    <color name="teal_surfaceContainerHigh">#252B2A</color>
    <color name="teal_surfaceContainerHighest">#303634</color>
    <color name="green_primary">#A2D399</color>
    <color name="green_onPrimary">#0C390E</color>
    <color name="green_primaryContainer">#255023</color>
    <color name="green_onPrimaryContainer">#BDF0B3</color>
    <color name="green_secondary">#BACCB3</color>
    <color name="green_onSecondary">#253423</color>
    <color name="green_secondaryContainer">#3B4B38</color>
    <color name="green_onSecondaryContainer">#D6E8CE</color>
    <color name="green_tertiary">#A0CFD4</color>
    <color name="green_onTertiary">#00363B</color>
    <color name="green_tertiaryContainer">#1E4D52</color>
    <color name="green_onTertiaryContainer">#BCEBF0</color>
    <color name="green_error">#FFB4AB</color>
    <color name="green_onError">#690005</color>
    <color name="green_errorContainer">#93000A</color>
    <color name="green_onErrorContainer">#FFDAD6</color>
    <color name="green_background">#10140F</color>
    <color name="green_onBackground">#E0E4DA</color>
    <color name="green_surface">#10140F</color>
    <color name="green_onSurface">#E0E4DA</color>
    <color name="green_surfaceVariant">#424940</color>
    <color name="green_onSurfaceVariant">#C2C8BD</color>
    <color name="green_outline">#8C9388</color>
    <color name="green_outlineVariant">#424940</color>
    <color name="green_inverseSurface">#E0E4DA</color>
    <color name="green_inverseOnSurface">#2D322B</color>
    <color name="green_inversePrimary">#3C6838</color>
    <color name="green_primaryFixed">#BDF0B3</color>
    <color name="green_onPrimaryFixed">#002203</color>
    <color name="green_primaryFixedDim">#A2D399</color>
    <color name="green_onPrimaryFixedVariant">#255023</color>
    <color name="green_secondaryFixed">#D6E8CE</color>
    <color name="green_onSecondaryFixed">#111F0F</color>
    <color name="green_secondaryFixedDim">#BACCB3</color>
    <color name="green_onSecondaryFixedVariant">#3B4B38</color>
    <color name="green_tertiaryFixed">#BCEBF0</color>
    <color name="green_onTertiaryFixed">#002022</color>
    <color name="green_tertiaryFixedDim">#A0CFD4</color>
    <color name="green_onTertiaryFixedVariant">#1E4D52</color>
    <color name="green_surfaceDim">#10140F</color>
    <color name="green_surfaceBright">#363A34</color>
    <color name="green_surfaceContainerLowest">#0B0F0A</color>
    <color name="green_surfaceContainerLow">#191D17</color>
    <color name="green_surfaceContainer">#1D211B</color>
    <color name="green_surfaceContainerHigh">#272B25</color>
    <color name="green_surfaceContainerHighest">#323630</color>
    <color name="deep_green_primary">#B0D18B</color>
    <color name="deep_green_onPrimary">#1E3702</color>
    <color name="deep_green_primaryContainer">#334E17</color>
    <color name="deep_green_onPrimaryContainer">#CBEDA5</color>
    <color name="deep_green_secondary">#BFCBAD</color>
    <color name="deep_green_onSecondary">#2A331F</color>
    <color name="deep_green_secondaryContainer">#404A34</color>
    <color name="deep_green_onSecondaryContainer">#DBE7C8</color>
    <color name="deep_green_tertiary">#A0CFCC</color>
    <color name="deep_green_onTertiary">#003735</color>
    <color name="deep_green_tertiaryContainer">#1F4E4C</color>
    <color name="deep_green_onTertiaryContainer">#BBECE8</color>
    <color name="deep_green_error">#FFB4AB</color>
    <color name="deep_green_onError">#690005</color>
    <color name="deep_green_errorContainer">#93000A</color>
    <color name="deep_green_onErrorContainer">#FFDAD6</color>
    <color name="deep_green_background">#12140E</color>
    <color name="deep_green_onBackground">#E2E3D8</color>
    <color name="deep_green_surface">#12140E</color>
    <color name="deep_green_onSurface">#E2E3D8</color>
    <color name="deep_green_surfaceVariant">#44483D</color>
    <color name="deep_green_onSurfaceVariant">#C4C8BA</color>
    <color name="deep_green_outline">#8E9285</color>
    <color name="deep_green_outlineVariant">#44483D</color>
    <color name="deep_green_inverseSurface">#E2E3D8</color>
    <color name="deep_green_inverseOnSurface">#2F312A</color>
    <color name="deep_green_inversePrimary">#4A672D</color>
    <color name="deep_green_primaryFixed">#CBEDA5</color>
    <color name="deep_green_onPrimaryFixed">#0E2000</color>
    <color name="deep_green_primaryFixedDim">#B0D18B</color>
    <color name="deep_green_onPrimaryFixedVariant">#334E17</color>
    <color name="deep_green_secondaryFixed">#DBE7C8</color>
    <color name="deep_green_onSecondaryFixed">#151E0B</color>
    <color name="deep_green_secondaryFixedDim">#BFCBAD</color>
    <color name="deep_green_onSecondaryFixedVariant">#404A34</color>
    <color name="deep_green_tertiaryFixed">#BBECE8</color>
    <color name="deep_green_onTertiaryFixed">#00201F</color>
    <color name="deep_green_tertiaryFixedDim">#A0CFCC</color>
    <color name="deep_green_onTertiaryFixedVariant">#1F4E4C</color>
    <color name="deep_green_surfaceDim">#12140E</color>
    <color name="deep_green_surfaceBright">#373A33</color>
    <color name="deep_green_surfaceContainerLowest">#0C0F09</color>
    <color name="deep_green_surfaceContainerLow">#1A1C16</color>
    <color name="deep_green_surfaceContainer">#1E211A</color>
    <color name="deep_green_surfaceContainerHigh">#282B24</color>
    <color name="deep_green_surfaceContainerHighest">#33362E</color>
    <color name="lime_primary">#C3CD7B</color>
    <color name="lime_onPrimary">#2E3400</color>
    <color name="lime_primaryContainer">#434B05</color>
    <color name="lime_onPrimaryContainer">#E0E995</color>
    <color name="lime_secondary">#C7C9A7</color>
    <color name="lime_onSecondary">#2F321A</color>
    <color name="lime_secondaryContainer">#46492F</color>
    <color name="lime_onSecondaryContainer">#E3E5C1</color>
    <color name="lime_tertiary">#A2D0C1</color>
    <color name="lime_onTertiary">#06372D</color>
    <color name="lime_tertiaryContainer">#224E43</color>
    <color name="lime_onTertiaryContainer">#BEECDD</color>
    <color name="lime_error">#FFB4AB</color>
    <color name="lime_onError">#690005</color>
    <color name="lime_errorContainer">#93000A</color>
    <color name="lime_onErrorContainer">#FFDAD6</color>
    <color name="lime_background">#13140D</color>
    <color name="lime_onBackground">#E5E3D6</color>
    <color name="lime_surface">#13140D</color>
    <color name="lime_onSurface">#E5E3D6</color>
    <color name="lime_surfaceVariant">#47483B</color>
    <color name="lime_onSurfaceVariant">#C8C7B7</color>
    <color name="lime_outline">#929283</color>
    <color name="lime_outlineVariant">#47483B</color>
    <color name="lime_inverseSurface">#E5E3D6</color>
    <color name="lime_inverseOnSurface">#313128</color>
    <color name="lime_inversePrimary">#5B631E</color>
    <color name="lime_primaryFixed">#E0E995</color>
    <color name="lime_onPrimaryFixed">#1A1E00</color>
    <color name="lime_primaryFixedDim">#C3CD7B</color>
    <color name="lime_onPrimaryFixedVariant">#434B05</color>
    <color name="lime_secondaryFixed">#E3E5C1</color>
    <color name="lime_onSecondaryFixed">#1B1D07</color>
    <color name="lime_secondaryFixedDim">#C7C9A7</color>
    <color name="lime_onSecondaryFixedVariant">#46492F</color>
    <color name="lime_tertiaryFixed">#BEECDD</color>
    <color name="lime_onTertiaryFixed">#002019</color>
    <color name="lime_tertiaryFixedDim">#A2D0C1</color>
    <color name="lime_onTertiaryFixedVariant">#224E43</color>
    <color name="lime_surfaceDim">#13140D</color>
    <color name="lime_surfaceBright">#393A31</color>
    <color name="lime_surfaceContainerLowest">#0E0F08</color>
    <color name="lime_surfaceContainerLow">#1B1C14</color>
    <color name="lime_surfaceContainer">#202018</color>
    <color name="lime_surfaceContainerHigh">#2A2B22</color>
    <color name="lime_surfaceContainerHighest">#35352D</color>
    <color name="yellow_primary">#F0BE6D</color>
    <color name="yellow_onPrimary">#432C00</color>
    <color name="yellow_primaryContainer">#604100</color>
    <color name="yellow_onPrimaryContainer">#FFDEAC</color>
    <color name="yellow_secondary">#DBC3A1</color>
    <color name="yellow_onSecondary">#3D2E16</color>
    <color name="yellow_secondaryContainer">#55442A</color>
    <color name="yellow_onSecondaryContainer">#F8DFBB</color>
    <color name="yellow_tertiary">#B5CEA4</color>
    <color name="yellow_onTertiary">#213618</color>
    <color name="yellow_tertiaryContainer">#374C2C</color>
    <color name="yellow_onTertiaryContainer">#D1EABF</color>
    <color name="yellow_error">#FFB4AB</color>
    <color name="yellow_onError">#690005</color>
    <color name="yellow_errorContainer">#93000A</color>
    <color name="yellow_onErrorContainer">#FFDAD6</color>
    <color name="yellow_background">#17130B</color>
    <color name="yellow_onBackground">#ECE1D4</color>
    <color name="yellow_surface">#17130B</color>
    <color name="yellow_onSurface">#ECE1D4</color>
    <color name="yellow_surfaceVariant">#4E4539</color>
    <color name="yellow_onSurfaceVariant">#D2C4B4</color>
    <color name="yellow_outline">#9B8F80</color>
    <color name="yellow_outlineVariant">#4E4539</color>
    <color name="yellow_inverseSurface">#ECE1D4</color>
    <color name="yellow_inverseOnSurface">#362F27</color>
    <color name="yellow_inversePrimary">#7D570D</color>
    <color name="yellow_primaryFixed">#FFDEAC</color>
    <color name="yellow_onPrimaryFixed">#281900</color>
    <color name="yellow_primaryFixedDim">#F0BE6D</color>
    <color name="yellow_onPrimaryFixedVariant">#604100</color>
    <color name="yellow_secondaryFixed">#F8DFBB</color>
    <color name="yellow_onSecondaryFixed">#261904</color>
    <color name="yellow_secondaryFixedDim">#DBC3A1</color>
    <color name="yellow_onSecondaryFixedVariant">#55442A</color>
    <color name="yellow_tertiaryFixed">#D1EABF</color>
    <color name="yellow_onTertiaryFixed">#0D2005</color>
    <color name="yellow_tertiaryFixedDim">#B5CEA4</color>
    <color name="yellow_onTertiaryFixedVariant">#374C2C</color>
    <color name="yellow_surfaceDim">#17130B</color>
    <color name="yellow_surfaceBright">#3F382F</color>
    <color name="yellow_surfaceContainerLowest">#120D07</color>
    <color name="yellow_surfaceContainerLow">#201B13</color>
    <color name="yellow_surfaceContainer">#241F17</color>
    <color name="yellow_surfaceContainerHigh">#2F2921</color>
    <color name="yellow_surfaceContainerHighest">#3A342B</color>
    <color name="orange_primary">#FFB77B</color>
    <color name="orange_onPrimary">#4C2700</color>
    <color name="orange_primaryContainer">#6B3A05</color>
    <color name="orange_onPrimaryContainer">#FFDCC2</color>
    <color name="orange_secondary">#E3C0A5</color>
    <color name="orange_onSecondary">#412C19</color>
    <color name="orange_secondaryContainer">#5A422E</color>
    <color name="orange_onSecondaryContainer">#FFDCC2</color>
    <color name="orange_tertiary">#C4CB97</color>
    <color name="orange_onTertiary">#2D330D</color>
    <color name="orange_tertiaryContainer">#444A22</color>
    <color name="orange_onTertiaryContainer">#E0E7B1</color>
    <color name="orange_error">#FFB4AB</color>
    <color name="orange_onError">#690005</color>
    <color name="orange_errorContainer">#93000A</color>
    <color name="orange_onErrorContainer">#FFDAD6</color>
    <color name="orange_background">#19120C</color>
    <color name="orange_onBackground">#EFE0D6</color>
    <color name="orange_surface">#19120C</color>
    <color name="orange_onSurface">#EFE0D6</color>
    <color name="orange_surfaceVariant">#51443B</color>
    <color name="orange_onSurfaceVariant">#D6C3B6</color>
    <color name="orange_outline">#9E8E82</color>
    <color name="orange_outlineVariant">#51443B</color>
    <color name="orange_inverseSurface">#EFE0D6</color>
    <color name="orange_inverseOnSurface">#382F28</color>
    <color name="orange_inversePrimary">#88511D</color>
    <color name="orange_primaryFixed">#FFDCC2</color>
    <color name="orange_onPrimaryFixed">#2E1500</color>
    <color name="orange_primaryFixedDim">#FFB77B</color>
    <color name="orange_onPrimaryFixedVariant">#6B3A05</color>
    <color name="orange_secondaryFixed">#FFDCC2</color>
    <color name="orange_onSecondaryFixed">#2A1707</color>
    <color name="orange_secondaryFixedDim">#E3C0A5</color>
    <color name="orange_onSecondaryFixedVariant">#5A422E</color>
    <color name="orange_tertiaryFixed">#E0E7B1</color>
    <color name="orange_onTertiaryFixed">#191E00</color>
    <color name="orange_tertiaryFixedDim">#C4CB97</color>
    <color name="orange_onTertiaryFixedVariant">#444A22</color>
    <color name="orange_surfaceDim">#19120C</color>
    <color name="orange_surfaceBright">#413731</color>
    <color name="orange_surfaceContainerLowest">#140D08</color>
    <color name="orange_surfaceContainerLow">#221A14</color>
    <color name="orange_surfaceContainer">#261E18</color>
    <color name="orange_surfaceContainerHigh">#312822</color>
    <color name="orange_surfaceContainerHighest">#3C332C</color>
    <color name="brown_primary">#E7BDB0</color>
    <color name="brown_onPrimary">#442A21</color>
    <color name="brown_primaryContainer">#6D4E43</color>
    <color name="brown_onPrimaryContainer">#FFF7F5</color>
    <color name="brown_secondary">#D8C2BB</color>
    <color name="brown_onSecondary">#3B2D29</color>
    <color name="brown_secondaryContainer">#483A35</color>
    <color name="brown_onSecondaryContainer">#E2CCC5</color>
    <color name="brown_tertiary">#CFC7A1</color>
    <color name="brown_onTertiary">#353116</color>
    <color name="brown_tertiaryContainer">#5B5537</color>
    <color name="brown_onTertiaryContainer">#FFF8E3</color>
    <color name="brown_error">#FFB4AB</color>
    <color name="brown_onError">#690005</color>
    <color name="brown_errorContainer">#93000A</color>
    <color name="brown_onErrorContainer">#FFDAD6</color>
    <color name="brown_background">#161312</color>
    <color name="brown_onBackground">#E9E1DF</color>
    <color name="brown_surface">#161312</color>
    <color name="brown_onSurface">#E9E1DF</color>
    <color name="brown_surfaceVariant">#504440</color>
    <color name="brown_onSurfaceVariant">#D4C3BE</color>
    <color name="brown_outline">#9D8E89</color>
    <color name="brown_outlineVariant">#504440</color>
    <color name="brown_inverseSurface">#E9E1DF</color>
    <color name="brown_inverseOnSurface">#33302E</color>
    <color name="brown_inversePrimary">#77574C</color>
    <color name="brown_primaryFixed">#FFDBCF</color>
    <color name="brown_onPrimaryFixed">#2C160D</color>
    <color name="brown_primaryFixedDim">#E7BDB0</color>
    <color name="brown_onPrimaryFixedVariant">#5D4036</color>
    <color name="brown_secondaryFixed">#F5DED6</color>
    <color name="brown_onSecondaryFixed">#251915</color>
    <color name="brown_secondaryFixedDim">#D8C2BB</color>
    <color name="brown_onSecondaryFixedVariant">#53433E</color>
    <color name="brown_tertiaryFixed">#ECE3BB</color>
    <color name="brown_onTertiaryFixed">#201C04</color>
    <color name="brown_tertiaryFixedDim">#CFC7A1</color>
    <color name="brown_onTertiaryFixedVariant">#4C472A</color>
    <color name="brown_surfaceDim">#161312</color>
    <color name="brown_surfaceBright">#3C3837</color>
    <color name="brown_surfaceContainerLowest">#100E0D</color>
    <color name="brown_surfaceContainerLow">#1E1B1A</color>
    <color name="brown_surfaceContainer">#221F1E</color>
    <color name="brown_surfaceContainerHigh">#2D2928</color>
    <color name="brown_surfaceContainerHighest">#383433</color>
    <color name="grey_primary">#C7C6C6</color>
    <color name="grey_onPrimary">#303031</color>
    <color name="grey_primaryContainer">#6C6C6C</color>
    <color name="grey_onPrimaryContainer">#FFFFFF</color>
    <color name="grey_secondary">#C8C6C5</color>
    <color name="grey_onSecondary">#303030</color>
    <color name="grey_secondaryContainer">#3E3D3D</color>
    <color name="grey_onSecondaryContainer">#D3D0D0</color>
    <color name="grey_tertiary">#CBC5C7</color>
    <color name="grey_onTertiary">#323031</color>
    <color name="grey_tertiaryContainer">#6F6B6D</color>
    <color name="grey_onTertiaryContainer">#FFFFFF</color>
    <color name="grey_error">#FFB4AB</color>
    <color name="grey_onError">#690005</color>
    <color name="grey_errorContainer">#93000A</color>
    <color name="grey_onErrorContainer">#FFDAD6</color>
    <color name="grey_background">#141313</color>
    <color name="grey_onBackground">#E5E2E1</color>
    <color name="grey_surface">#141313</color>
    <color name="grey_onSurface">#E5E2E1</color>
    <color name="grey_surfaceVariant">#444748</color>
    <color name="grey_onSurfaceVariant">#C4C7C7</color>
    <color name="grey_outline">#8E9192</color>
    <color name="grey_outlineVariant">#444748</color>
    <color name="grey_inverseSurface">#E5E2E1</color>
    <color name="grey_inverseOnSurface">#313030</color>
    <color name="grey_inversePrimary">#5E5E5E</color>
    <color name="grey_primaryFixed">#E3E2E2</color>
    <color name="grey_onPrimaryFixed">#1B1C1C</color>
    <color name="grey_primaryFixedDim">#C7C6C6</color>
    <color name="grey_onPrimaryFixedVariant">#464747</color>
    <color name="grey_secondaryFixed">#E5E2E1</color>
    <color name="grey_onSecondaryFixed">#1B1C1C</color>
    <color name="grey_secondaryFixedDim">#C8C6C5</color>
    <color name="grey_onSecondaryFixedVariant">#474746</color>
    <color name="grey_tertiaryFixed">#E7E1E3</color>
    <color name="grey_onTertiaryFixed">#1D1B1D</color>
    <color name="grey_tertiaryFixedDim">#CBC5C7</color>
    <color name="grey_onTertiaryFixedVariant">#494648</color>
    <color name="grey_surfaceDim">#141313</color>
    <color name="grey_surfaceBright">#3A3939</color>
    <color name="grey_surfaceContainerLowest">#0E0E0E</color>
    <color name="grey_surfaceContainerLow">#1C1B1B</color>
    <color name="grey_surfaceContainer">#201F1F</color>
    <color name="grey_surfaceContainerHigh">#2A2A2A</color>
    <color name="grey_surfaceContainerHighest">#353434</color>
</resources>