<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <org.oxycblt.auxio.image.CoverView
        android:id="@+id/playback_cover"
        style="@style/Widget.Auxio.Image.Small"
        android:layout_margin="@dimen/spacing_small"
        app:enablePlaybackIndicator="false"
        app:enableSelectionBadge="false"
        app:layout_constraintBottom_toTopOf="@+id/playback_progress_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/playback_song"
        style="@style/Widget.Auxio.TextView.Primary.Compact"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/spacing_small"
        android:layout_marginEnd="@dimen/spacing_tiny"
        app:layout_constraintBottom_toTopOf="@+id/playback_info"
        app:layout_constraintEnd_toStartOf="@+id/playback_controls_wrapper"
        app:layout_constraintStart_toEndOf="@+id/playback_cover"
        app:layout_constraintTop_toTopOf="@+id/playback_cover"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Song Name" />

    <TextView
        android:id="@+id/playback_info"
        style="@style/Widget.Auxio.TextView.Secondary.Compact"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/spacing_small"
        android:layout_marginEnd="@dimen/spacing_tiny"
        android:ellipsize="marquee"
        android:marqueeRepeatLimit="marquee_forever"
        app:layout_constraintBottom_toBottomOf="@+id/playback_cover"
        app:layout_constraintEnd_toStartOf="@+id/playback_controls_wrapper"
        app:layout_constraintStart_toEndOf="@+id/playback_cover"
        app:layout_constraintTop_toBottomOf="@+id/playback_song"
        tools:text="Artist Name" />

    <org.oxycblt.auxio.playback.ui.ForcedLTRFrameLayout
        android:id="@+id/playback_controls_wrapper"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/spacing_tiny"
        app:layout_constraintBottom_toTopOf="@+id/playback_progress_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/playback_controls_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <org.oxycblt.auxio.ui.RippleFixMaterialButton
                android:id="@+id/playback_play_pause"
                style="@style/Widget.Auxio.Button.Icon.Small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="@string/desc_play_pause"
                app:icon="@drawable/sel_playing_state_24" />

            <org.oxycblt.auxio.ui.RippleFixMaterialButton
                android:id="@+id/playback_secondary_action"
                style="@style/Widget.Auxio.Button.Icon.Small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:icon="@drawable/ic_skip_next_24" />
        </LinearLayout>

    </org.oxycblt.auxio.playback.ui.ForcedLTRFrameLayout>

    <org.oxycblt.auxio.playback.ui.ForcedLTRFrameLayout
        android:id="@+id/playback_progress_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/playback_progress_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/spacing_small"
            android:layout_marginEnd="@dimen/spacing_small"
            tools:progress="70" />

    </org.oxycblt.auxio.playback.ui.ForcedLTRFrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
