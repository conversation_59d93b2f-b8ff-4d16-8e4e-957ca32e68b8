<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
    <!-- Info namespace | App labels -->
    <eat-comment />

    <string name="info_app_desc">A simple, rational music player for android.</string>

    <!-- Label Namespace | Static Labels -->
    <eat-comment />

    <!-- Not an active task -->
    <string name="lbl_indexer">Music loading</string>
    <!-- Is an active task -->
    <string name="lbl_indexing">Loading music</string>
    <!-- Monitoring music library for changes -->
    <string name="lbl_observing">Monitoring music library</string>
    <!-- As in to retry loading music -->
    <string name="lbl_retry">Retry</string>
    <string name="lbl_music_sources">Pick folders</string>
    <!-- As in to show additional information about a music loading error -->
    <!-- As in grant permission -->

    <string name="lbl_songs">Songs</string>
    <string name="lbl_song">Song</string>
    <string name="lbl_all_songs">All songs</string>

    <string name="lbl_albums">Albums</string>
    <string name="lbl_album">Album</string>
    <!-- As in the the music release being played live -->
    <string name="lbl_album_live">Live album</string>
    <!-- As in a remix of the music release -->
    <string name="lbl_album_remix">Remix album</string>

    <!-- As in the type of music release -->
    <string name="lbl_eps">EPs</string>
    <!-- As in the type of music release -->
    <string name="lbl_ep">EP</string>
    <!-- As in the the music release being played live -->
    <string name="lbl_ep_live">Live EP</string>
    <!-- As in a remix of the music release -->
    <string name="lbl_ep_remix">Remix EP</string>

    <!-- As in the type of music release -->
    <string name="lbl_singles">Singles</string>
    <!-- As in the type of music release -->
    <string name="lbl_single">Single</string>
    <!-- As in the the music release being played live -->
    <string name="lbl_single_live">Live single</string>
    <!-- As in a remix of the music release -->
    <string name="lbl_single_remix">Remix single</string>

    <!-- As in a compilation of music -->
    <string name="lbl_compilations">Compilations</string>
    <!-- As in a compilation of music -->
    <string name="lbl_compilation">Compilation</string>
    <!-- As in a compilation of live music -->
    <string name="lbl_compilation_live">Live compilation</string>
    <string name="lbl_compilation_remix">Remix compilation</string>
    <string name="lbl_soundtracks">Soundtracks</string>
    <string name="lbl_soundtrack">Soundtrack</string>
    <!-- As in the collection of music -->
    <string name="lbl_mixtapes">Mixtapes</string>
    <!-- As in the collection of music -->
    <string name="lbl_mixtape">Mixtape</string>
    <!-- As in the collection of music -->
    <string name="lbl_demo">Demo</string>
    <!-- AS in the collection of music -->
    <string name="lbl_demos">Demos</string>
    <!-- As in a compilation of several performances that blend into a single continuous flow of music (Also known as DJ Mixes) -->
    <string name="lbl_mixes">DJ Mixes</string>
    <!-- As in a compilation of several performances that blend into a single continuous flow of music (Also known as DJ Mixes) -->
    <string name="lbl_mix">DJ Mix</string>

    <!-- As in music that was performed live -->
    <string name="lbl_live_group">Live</string>
    <!-- As in remixed music -->
    <string name="lbl_remix_group">Remixes</string>
    <!-- As in albums that an artist only partially contributed to -->
    <string name="lbl_appears_on">Appears on</string>

    <string name="lbl_artist">Artist</string>
    <string name="lbl_artists">Artists</string>

    <string name="lbl_genre">Genre</string>
    <string name="lbl_genres">Genres</string>

    <string name="lbl_playlist">Playlist</string>
    <string name="lbl_playlists">Playlists</string>
    <string name="lbl_new_playlist">New playlist</string>
    <string name="lbl_empty_playlist">Empty playlist</string>
    <string name="lbl_imported_playlist">Imported playlist</string>
    <string name="lbl_import">Import</string>
    <string name="lbl_import_playlist">Import playlist</string>
    <string name="lbl_export">Export</string>
    <string name="lbl_export_playlist">Export playlist</string>
    <string name="lbl_rename">Rename</string>
    <string name="lbl_rename_playlist">Rename playlist</string>
    <string name="lbl_delete">Delete</string>
    <string name="lbl_confirm_delete_playlist">Delete playlist?</string>
    <string name="lbl_edit">Edit</string>

    <!-- Search for music -->
    <string name="lbl_search">Search</string>
    <!-- As in filtering to particular types of music in the search view -->
    <string name="lbl_filter">Filter</string>
    <!-- As in to not filter -->
    <string name="lbl_filter_all">All</string>

    <string name="lbl_name">Name</string>
    <string name="lbl_date">Date</string>
    <string name="lbl_duration">Duration</string>
    <string name="lbl_song_count">Song count</string>
    <string name="lbl_disc">Disc</string>
    <string name="lbl_track">Track</string>
    <string name="lbl_date_added">Date added</string>

    <string name="lbl_sort">Sort</string>
    <string name="lbl_sort_mode">Sort by</string>
    <string name="lbl_sort_direction">Direction</string>
    <string name="lbl_sort_asc">Ascending</string>
    <string name="lbl_sort_dsc">Descending</string>

    <string name="lbl_playback">Now playing</string>
    <string name="lbl_equalizer">Equalizer</string>
    <string name="lbl_play">Play</string>
    <string name="lbl_shuffle">Shuffle</string>

    <string name="lbl_queue">Queue</string>
    <string name="lbl_play_next">Play next</string>
    <string name="lbl_queue_add">Add to queue</string>

    <string name="lbl_playlist_add">Add to playlist</string>

    <string name="lbl_artist_details">Go to artist</string>
    <string name="lbl_album_details">Go to album</string>
    <string name="lbl_song_detail">View properties</string>
    <string name="lbl_parent_detail">View</string>
    <string name="lbl_share">Share</string>

    <string name="lbl_props">Song properties</string>
    <string name="lbl_path">Path</string>
    <!-- As in audio format -->
    <string name="lbl_format">Format</string>
    <!-- As in file size -->
    <string name="lbl_size">Size</string>
    <string name="lbl_bitrate">Bit rate</string>
    <string name="lbl_sample_rate">Sample rate</string>
    <string name="lbl_replaygain_track">ReplayGain Track Adjustment</string>
    <string name="lbl_replaygain_album">ReplayGain Album Adjustment</string>

    <!-- Limit to 10 characters -->
    <string name="lbl_shuffle_shortcut_short">Shuffle</string>
    <!-- Limit to 25 characters -->
    <string name="lbl_shuffle_shortcut_long">Shuffle all</string>
    <string name="lbl_start_playback">Start playback</string>

    <string name="lbl_ok">OK</string>
    <string name="lbl_cancel">Cancel</string>
    <!-- As in to save a setting -->
    <string name="lbl_save">Save</string>
    <!-- as in to reset to a default setting -->
    <string name="lbl_reset">Reset</string>
    <!-- As in to add a new folder in the "Music folders" setting -->
    <string name="lbl_more">More</string>

    <string name="lbl_path_style">Path style</string>
    <string name="lbl_path_style_absolute">Absolute</string>
    <string name="lbl_path_style_relative">Relative</string>

    <string name="lbl_windows_paths">Use Windows-compatible paths</string>

    <string name="lbl_about">About</string>
    <string name="lbl_version">Version</string>
    <string name="lbl_code">Source code</string>
    <string name="lbl_wiki">Wiki</string>
    <string name="lbl_licenses">Licenses</string>
    <string name="lbl_library_counts">Library statistics</string>

    <string name="lbl_selection">Selection</string>

    <string name="lbl_error_info">Error information</string>
    <!-- As in copied to the system clipboard -->
    <string name="lbl_copied">Copied</string>
    <!-- As in to report an error -->
    <string name="lbl_report">Report</string>

    <string name="lbl_author">Author</string>
    <string name="lbl_author_name">Alexander Capehart</string>
    <string name="lbl_feedback">Feedback</string>
    <string name="lbl_github">Make an issue on GitHub</string>
    <string name="lbl_email">Send an email</string>
    <string name="lbl_donate">Donate</string>
    <string name="lbl_supporters">Supporters</string>

    <!-- Long Namespace | Longer Descriptions -->
    <eat-comment />

    <string name="lng_widget">View and control music playback</string>
    <string name="lng_indexing">Loading your music library…</string>
    <string name="lng_observing">Monitoring your music library for changes…</string>
    <string name="lng_queue_added">Added to queue</string>
    <string name="lng_playlist_created">Playlist created</string>
    <string name="lng_playlist_imported">Playlist imported</string>
    <string name="lng_playlist_renamed">Playlist renamed</string>
    <string name="lng_playlist_exported">Playlist exported</string>
    <string name="lng_playlist_deleted">Playlist deleted</string>
    <string name="lng_playlist_added">Added to playlist</string>
    <string name="lng_supporters_promo">Donate to the project to get your name added here!</string>
    <!-- As in music library -->
    <string name="lng_search_library">Search your library…</string>
    <string name="lng_tasker_start">
        Starts Auxio using the previously saved state. If no saved state is available, all songs will be shuffled. Playback will start immediately.
        \n\nWARNING: Be careful controlling this service, if you close it and then try to use it again, you will probably crash the app.
    </string>
    <string name="lng_empty_songs">Your songs will show up here.</string>
    <string name="lng_empty_albums">Your albums will show up here.</string>
    <string name="lng_empty_artists">Your artists will show up here.</string>
    <string name="lng_empty_genres">Your genres will show up here.</string>
    <string name="lng_empty_playlists">Your playlists will show up here.</string>

    <!-- Settings namespace | Settings-related labels -->
    <eat-comment />

    <string name="set_root_title">Settings</string>

    <string name="set_ui">Look and Feel</string>
    <string name="set_ui_desc">Change the theme and colors of the app</string>
    <string name="set_theme">Theme</string>
    <string name="set_theme_auto">Automatic</string>
    <string name="set_theme_day">Light</string>
    <string name="set_theme_night">Dark</string>
    <string name="set_accent">Color scheme</string>
    <string name="set_black_mode">Black theme</string>
    <string name="set_black_mode_desc">Use a pure-black dark theme</string>
    <string name="set_round_mode">Round mode</string>
    <string name="set_round_mode_desc">Enable rounded corners on additional UI elements (requires album covers to be rounded)</string>

    <string name="set_personalize">Personalize</string>
    <string name="set_personalize_desc">Customize UI controls and behavior</string>
    <string name="set_display">Display</string>
    <string name="set_lib_tabs">Library tabs</string>
    <string name="set_lib_tabs_desc">Change visibility and order of library tabs</string>
    <!-- Skip to next (song) -->
    <string name="set_bar_action">Custom playback bar action</string>
    <string name="set_notif_action">Custom notification action</string>
    <string name="set_action_mode_next">Skip to next</string>
    <string name="set_action_mode_repeat">Repeat mode</string>
    <string name="set_behavior">Behavior</string>
    <string name="set_play_in_list_with">When playing from the library</string>
    <string name="set_play_in_parent_with">When playing from item details</string>
    <string name="set_play_song_none">Play from shown item</string>
    <string name="set_play_song_from_all">Play from all songs</string>
    <string name="set_play_song_from_album">Play from album</string>
    <string name="set_play_song_from_artist">Play from artist</string>
    <string name="set_play_song_from_genre">Play from genre</string>
    <string name="set_play_song_by_itself">Play song by itself</string>
    <string name="set_keep_shuffle">Remember shuffle</string>
    <string name="set_keep_shuffle_desc">Keep shuffle on when playing a new song</string>

    <string name="set_content">Content</string>
    <string name="set_content_desc">Control how music and images are loaded</string>
    <string name="set_music">Music</string>
    <string name="set_observing">Automatic reloading</string>
    <string name="set_observing_desc">Reload the music library whenever it changes (requires persistent notification)</string>
    <string name="set_exclude_non_music">Exclude non-music</string>
    <string name="set_exclude_non_music_desc">Ignore audio files that are not music, such as podcasts</string>
    <string name="set_with_hidden">Include hidden files</string>
    <string name="set_with_hidden_desc">Include audio files that are hidden (ex. .cache)</string>
    <string name="set_separators">Multi-value separators</string>
    <string name="set_separators_desc">Configure characters that denote multiple tag values</string>
    <string name="set_separators_comma">Comma (,)</string>
    <string name="set_separators_semicolon">Semicolon (;)</string>
    <string name="set_separators_slash">Slash (/)</string>
    <string name="set_separators_plus">Plus (+)</string>
    <string name="set_separators_and">Ampersand (&amp;)</string>
    <string name="set_separators_warning">Warning: Using this setting may result in some tags being incorrectly interpreted as having multiple values. You can resolve this by prefixing unwanted separator characters with a backslash (\\).</string>
    <string name="set_intelligent_sorting">Intelligent sorting</string>
    <string name="set_intelligent_sorting_desc">Correctly sort names that begin with numbers or words like \"the\" (works best with english-language music)</string>
    <string name="set_hide_collaborators">Hide collaborators</string>
    <string name="set_hide_collaborators_desc">Only show artists that are directly credited on an album (works best on well-tagged libraries)</string>
    <string name="set_images">Images</string>
    <string name="set_cover_mode">Album covers</string>
    <string name="set_cover_mode_off">Off</string>
    <string name="set_cover_mode_save_space">Save space</string>
    <string name="set_cover_mode_balanced">Balanced</string>
    <string name="set_cover_mode_high_quality">High quality</string>
    <string name="set_cover_mode_as_is">As is</string>
    <string name="set_square_covers">Force square album covers</string>
    <string name="set_square_covers_desc">Crop all album covers to a 1:1 aspect ratio</string>

    <string name="set_audio">Audio</string>
    <string name="set_audio_desc">Configure sound and playback behavior</string>
    <string name="set_playback">Playback</string>
    <string name="set_headset_autoplay">Headset autoplay</string>
    <string name="set_headset_autoplay_desc">Always start playing when a headset is connected (may not work on all devices)</string>
    <string name="set_rewind_prev">Rewind before skipping back</string>
    <string name="set_rewind_prev_desc">Rewind before skipping to the previous song</string>
    <string name="set_repeat_pause">Pause on repeat</string>
    <string name="set_repeat_pause_desc">Pause when a song repeats</string>
    <string name="set_remember_pause">Remember pause</string>
    <string name="set_remember_pause_desc">Remain playing/paused when skipping or editing queue</string>
    <string name="set_replay_gain">Volume normalization</string>
    <string name="set_replay_gain_mode">ReplayGain strategy</string>
    <string name="set_replay_gain_mode_off">Off</string>
    <string name="set_replay_gain_mode_track">Prefer track</string>
    <string name="set_replay_gain_mode_album">Prefer album</string>
    <string name="set_replay_gain_mode_dynamic">Prefer album if one is playing</string>
    <string name="set_pre_amp">ReplayGain pre-amp</string>
    <string name="set_pre_amp_desc">The pre-amp is applied to the existing adjustment during playback</string>
    <string name="set_pre_amp_with">Adjustment with tags</string>
    <string name="set_pre_amp_without">Adjustment without tags</string>
    <string name="set_pre_amp_warning">Warning: Changing the pre-amp to a high positive value may result in peaking on some audio tracks.</string>

    <string name="set_library">Library</string>
    <string name="set_locations">Music folders</string>
    <string name="set_locations_desc">Manage where music should be loaded from</string>
    <string name="set_locations_new">New folder</string>
    <string name="set_fs_cache">Use file-system cache</string>
    <string name="set_fs_cache_desc">Improves loading times, but can prevent new music from being added</string>
    <string name="set_excluded_locations">Excluded folders</string>
    <string name="set_excluded_locations_desc">Manage folders to exclude from music loading</string>
    <string name="set_reindex">Refresh music</string>
    <string name="set_reindex_desc">Reload the music library, using cached tags when possible</string>
    <!-- Different from "Reload music" -->
    <string name="set_rescan">Rescan music</string>
    <string name="set_rescan_desc">Clear the tag cache and fully reload the music library (slower, but more complete)</string>

    <!-- Error Namespace | Error Labels -->
    <string name="err_index_failed">Music loading failed</string>
    <string name="err_import_failed">Unable to import a playlist from this file</string>
    <string name="err_export_failed">Unable to export the playlist to this file</string>
    <string name="err_no_app">No app found that can handle this task</string>
    <!-- No folders in the "Music Folders" setting -->
    <string name="err_bad_location">This folder is not supported</string>

    <!-- Description Namespace | Accessibility Strings -->

    <string name="desc_track_number">Track %d</string>

    <string name="desc_play_pause">Play or pause</string>
    <string name="desc_skip_next">Skip to next song</string>
    <string name="desc_skip_prev">Skip to last song</string>
    <string name="desc_change_repeat">Change repeat mode</string>
    <string name="desc_shuffle">Turn shuffle on or off</string>
    <string name="desc_exit">Stop playback</string>

    <string name="desc_remove_song">Remove this song</string>
    <string name="desc_song_handle">Move this song</string>
    <string name="desc_queue_bar">Open the queue</string>
    <string name="desc_tab_handle">Move this tab</string>
    <string name="desc_clear_search">Clear search query</string>
    <string name="desc_music_location_delete">Remove folder</string>

    <string name="desc_auxio_icon">Auxio icon</string>
    <string name="desc_no_cover">Album cover</string>
    <string name="desc_album_cover">Album cover for %s</string>
    <string name="desc_artist_image">Artist image for %s</string>
    <string name="desc_genre_image">Genre image for %s</string>
    <string name="desc_playlist_image">Playlist image for %s</string>
    <string name="desc_selection_image">Selection image</string>

    <!-- Default Namespace | Placeholder values -->
    <eat-comment />

    <string name="def_album">Unknown album</string>
    <string name="def_artist">Unknown artist</string>
    <string name="def_genre">Unknown genre</string>
    <string name="def_date">No date</string>
    <string name="def_disc">No disc</string>
    <string name="def_track">No track</string>
    <string name="def_song_count">No songs</string>
    <string name="def_album_count">No albums</string>
    <string name="def_playback">No music playing</string>

    <!-- Codec Namespace | Format names -->
    <eat-comment />

    <!-- "Audio" can optionally be translated -->
    <string name="cdc_mp3">MPEG-1 audio</string>
    <!-- "Audio" can optionally be translated -->
    <string name="cdc_mp4">MPEG-4 audio</string>
    <!-- i.e MPEG-4 containing some codec -->
    <string name="cnt_mp4">MPEG-4 containing %s</string>
    <!-- "Advanced Audio Coding" can optionally be translated -->
    <string name="cdc_aac">Advanced Audio Coding (AAC)</string>
    <!-- "Apple Lossless Audio Codec" can optionally be translated -->
    <string name="cdc_alac">Apple Lossless Audio Codec (ALAC)</string>
    <!-- "Audio" can optionally be translated -->
    <string name="cdc_ogg">Ogg audio</string>
    <!-- "Audio" can optionally be translated -->
    <!-- "Free Lossless Audio Codec" can optionally be translated -->
    <string name="cdc_flac">Free Lossless Audio Codec (FLAC)</string>
    <!-- As in unknown format -->
    <string name="cdc_unknown">Unknown</string>

    <!-- Color Label namespace | Accent names -->
    <eat-comment />

    <string name="clr_red">Red</string>
    <string name="clr_pink">Pink</string>
    <string name="clr_purple">Purple</string>
    <string name="clr_deep_purple">Deep purple</string>
    <string name="clr_indigo">Indigo</string>
    <string name="clr_blue">Blue</string>
    <string name="clr_deep_blue">Deep blue</string>
    <string name="clr_cyan">Cyan</string>
    <string name="clr_teal">Teal</string>
    <string name="clr_green">Green</string>
    <string name="clr_deep_green">Deep green</string>
    <string name="clr_lime">Lime</string>
    <string name="clr_yellow">Yellow</string>
    <string name="clr_orange">Orange</string>
    <string name="clr_brown">Brown</string>
    <string name="clr_grey">Grey</string>
    <!-- As in "Dynamic Colors"/Material You theming -->
    <string name="clr_dynamic">Dynamic</string>

    <!-- Format Namespace | Value formatting/plurals -->
    <eat-comment />

    <!--
    Comma (,) separator should be localized (For example, "、" in japanese).
    Do not use "and" or equivalents.
    -->
    <string name="fmt_list">%1$s, %2$s</string>

    <!-- As in an amount of items that are selected -->
    <string name="fmt_selected">%d Selected</string>
    <!-- Currently editing a playlist -->
    <string name="fmt_editing">Editing %s</string>

    <!-- As in "Disc 1", "Disc 2", etc. in a set -->
    <string name="fmt_disc_no">Disc %d</string>
    <!-- As in "Playlist 1", "Playlist 2", etc. -->
    <string name="fmt_def_playlist">Playlist %d</string>

    <!-- Use your native country's abbreviation for decibel units. -->
    <string name="fmt_db_pos">+%.1f dB</string>
    <!-- Use your native country's abbreviation for decibel units. -->
    <string name="fmt_db_neg">-%.1f dB</string>
    <!-- Use your native country's abbreviation for bit rate units. -->
    <string name="fmt_bitrate">%d kbps</string>
    <!-- Use your native country's abbreviation for hertz units. -->
    <string name="fmt_sample_rate">%d Hz</string>

    <string name="fmt_indexing">Loading your music library… (%1$d/%2$d)</string>
    <string name="fmt_deletion_info">Delete %s? This cannot be undone.</string>

    <string name="fmt_lib_song_count">Songs loaded: %d</string>
    <string name="fmt_lib_album_count">Albums loaded: %d</string>
    <string name="fmt_lib_artist_count">Artists loaded: %d</string>
    <string name="fmt_lib_genre_count">Genres loaded: %d</string>
    <!-- As in the total duration of all songs in the music library -->
    <string name="fmt_lib_total_duration">Total duration: %s</string>

    <plurals name="fmt_song_count">
        <item quantity="one">%d song</item>
        <item quantity="other">%d songs</item>
    </plurals>

    <plurals name="fmt_album_count">
        <item quantity="one">%d album</item>
        <item quantity="other">%d albums</item>
    </plurals>

    <plurals name="fmt_artist_count">
        <item quantity="one">%d artist</item>
        <item quantity="other">%d artists</item>
    </plurals>
</resources>
