// Copyright (C) 2014 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
apply from: "$gradle.ext.androidxMediaSettingsDir/common_library_config.gradle"

android {
    namespace 'androidx.media3.datasource.cronet'

    publishing {
        singleVariant('release') {
            withSourcesJar()
        }
    }
}

dependencies {
    api "com.google.android.gms:play-services-cronet:18.0.1"
    implementation project(modulePrefix + 'lib-common')
    implementation project(modulePrefix + 'lib-datasource')
    implementation 'androidx.annotation:annotation:' + androidxAnnotationVersion
    compileOnly 'com.google.errorprone:error_prone_annotations:' + errorProneVersion
    compileOnly 'org.checkerframework:checker-qual:' + checkerframeworkVersion
    compileOnly 'org.jetbrains.kotlin:kotlin-annotations-jvm:' + kotlinAnnotationsVersion
    androidTestImplementation 'androidx.test:rules:' + androidxTestRulesVersion
    androidTestImplementation 'androidx.test:runner:' + androidxTestRunnerVersion
    androidTestImplementation 'com.linkedin.dexmaker:dexmaker-mockito:' + dexmakerVersion
    // Instrumentation tests assume that an app-packaged version of cronet is
    // available.
    androidTestImplementation 'org.chromium.net:cronet-embedded:108.5359.79'
    androidTestImplementation project(modulePrefix + 'test-utils')
    testImplementation project(modulePrefix + 'test-utils')
    testImplementation 'com.squareup.okhttp3:mockwebserver:' + okhttpVersion
    testImplementation 'org.robolectric:robolectric:' + robolectricVersion
}

ext {
    releaseArtifactId = 'media3-datasource-cronet'
    releaseName = 'Media3 Cronet DataSource module'
}
apply from: '../../publish.gradle'
