<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- SHARED RE-USABLE UI STYLES -->

    <style name="Widget.Auxio.AppBarLayout" parent="Widget.Material3.AppBarLayout">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <!-- Fix flickering lift animation when scrolling quickly -->
        <item name="android:stateListAnimator">@null</item>
    </style>

    <!-- Base toolbar style -->
    <style name="Widget.Auxio.Toolbar" parent="Widget.Material3.Toolbar">
        <item name="maxButtonHeight">@dimen/size_touchable_small</item>
        <item name="android:paddingLeft">@dimen/spacing_tiny</item>
        <item name="android:paddingStart">@dimen/spacing_tiny</item>
        <item name="android:paddingRight">@dimen/spacing_tiny</item>
        <item name="android:paddingEnd">@dimen/spacing_tiny</item>
    </style>

    <style name="Widget.Auxio.Slider" parent="Widget.Material3.Slider">
        <item name="labelBehavior">gone</item>
        <item name="tickVisible">false</item>
        <item name="trackStopIndicatorSize">0dp</item>
        <item name="trackHeight">@dimen/slider_track_height</item>
        <item name="thumbHeight">@dimen/slider_thumb_height</item>
    </style>

    <style name="Widget.Auxio.LinearProgressIndicator" parent="Widget.Material3.LinearProgressIndicator">
<!--        <item name="trackColor">@color/sel_track</item>-->
        <item name="trackCornerRadius">@dimen/m3_shape_corners_full</item>
        <item name="trackStopIndicatorSize">0dp</item>
    </style>

    <style name="Widget.Auxio.BottomSheet" parent="Widget.Material3.BottomSheet">
        <item name="marginLeftSystemWindowInsets">false</item>
        <item name="marginRightSystemWindowInsets">false</item>
        <item name="paddingBottomSystemWindowInsets">false</item>
        <item name="paddingTopSystemWindowInsets">false</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Auxio.BottomSheet</item>
        <item name="shouldRemoveExpandedCorners">true</item>
    </style>

    <style name="ShapeAppearance.Auxio.BottomSheet" parent="ShapeAppearance.Material3.Corner.ExtraLarge">
        <!-- This is really badly off-spec, but is needed to nicely harmonize the corner radii in the playback bar -->
        <item name="cornerSize">20dp</item>
    </style>

    <style name="Widget.Auxio.BottomSheet.Modal" parent="Widget.Material3.BottomSheet.Modal">
        <item name="marginLeftSystemWindowInsets">false</item>
        <item name="marginRightSystemWindowInsets">false</item>
        <item name="paddingBottomSystemWindowInsets">false</item>
        <item name="paddingTopSystemWindowInsets">false</item>
        <item name="shouldRemoveExpandedCorners">false</item>
    </style>

    <style name="Widget.Auxio.BottomSheet.Dialog" parent="ThemeOverlay.Material3.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/Widget.Auxio.BottomSheet.Modal</item>
        <item name="android:windowAnimationStyle">@style/Animation.Auxio.BottomSheet.Dialog</item>
    </style>

    <style name="Animation.Auxio.BottomSheet.Dialog" parent="Animation.Material3.BottomSheetDialog">
        <item name="android:windowEnterAnimation">@anim/bottom_sheet_slide_in</item>
        <item name="android:windowExitAnimation">@anim/bottom_sheet_slide_out</item>
    </style>

    <style name="Widget.Auxio.BottomSheet.Handle" parent="Widget.Material3.BottomSheet.DragHandle">
        <item name="android:paddingBottom">0dp</item>
    </style>

    <style name="Widget.Auxio.Image.Small" parent="">
        <item name="android:layout_width">@dimen/size_touchable_small</item>
        <item name="android:layout_height">@dimen/size_touchable_small</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.Medium</item>
        <item name="iconSize">@dimen/size_icon_small</item>
    </style>

    <style name="Widget.Auxio.Image.Medium" parent="">
        <item name="android:layout_width">@dimen/size_touchable_medium</item>
        <item name="android:layout_height">@dimen/size_touchable_medium</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.Medium</item>
        <item name="iconSize">@dimen/size_icon_medium</item>
    </style>

    <style name="Widget.Auxio.Image.MidLarge" parent="">
        <item name="android:layout_width">@dimen/size_touchable_huge</item>
        <item name="android:layout_height">@dimen/size_touchable_huge</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.ExtraLarge</item>
    </style>

    <style name="Widget.Auxio.Image.Large" parent="">
        <item name="android:layout_width">@dimen/size_hero_small</item>
        <item name="android:layout_height">@dimen/size_hero_small</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.ExtraLarge</item>
    </style>

    <style name="Widget.Auxio.Image.MidHuge" parent="">
        <item name="android:layout_width">@dimen/size_hero_medium</item>
        <item name="android:layout_height">@dimen/size_hero_medium</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.ExtraLarge</item>
    </style>

    <style name="Widget.Auxio.Image.Huge" parent="">
        <item name="android:layout_width">@dimen/size_hero_large</item>
        <item name="android:layout_height">@dimen/size_hero_large</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.ExtraLarge</item>
    </style>

    <style name="Widget.Auxio.Image.MidFull" parent="">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
        <item name="layout_constraintDimensionRatio">1</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.Large</item>
    </style>

    <style name="Widget.Auxio.Image.Full" parent="">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
        <item name="layout_constraintDimensionRatio">1</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Material3.Corner.ExtraLarge</item>
    </style>

    <style name="Widget.Auxio.RecyclerView.Linear" parent="">
        <item name="layoutManager">androidx.recyclerview.widget.LinearLayoutManager</item>
    </style>

    <style name="Widget.Auxio.RecyclerView.Grid.Base" parent="">
        <item name="layoutManager">androidx.recyclerview.widget.GridLayoutManager</item>
    </style>

    <style name="Widget.Auxio.RecyclerView.Grid" parent="Widget.Auxio.RecyclerView.Grid.Base" />

    <style name="Widget.Auxio.RecyclerView.Grid.WithAdaptiveFab" parent="Widget.Auxio.RecyclerView.Grid">
        <item name="android:paddingBottom">@dimen/recycler_fab_space_normal</item>
    </style>

    <style name="Widget.Auxio.DisableDropShadows" parent="">
        <!-- API 28+ Only -->
        <item name="android:outlineAmbientShadowColor" tools:ignore="NewApi">
            @android:color/transparent
        </item>
        <item name="android:outlineSpotShadowColor" tools:ignore="NewApi">
            @android:color/transparent
        </item>
    </style>

    <style name="ThemeOverlay.Accent" parent="">
        <item name="colorOnPrimary">?attr/colorSurface</item>
    </style>

    <!-- TEXTVIEW STYLES -->

    <style name="Widget.Auxio.TextView.Base" parent="Widget.AppCompat.TextView">
        <item name="android:textAlignment">viewStart</item>
    </style>

    <style name="Widget.Auxio.TextView.Item.Base" parent="Widget.Auxio.TextView.Base">
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="android:layout_marginStart">@dimen/spacing_medium</item>
    </style>

    <style name="Widget.Auxio.TextView.Item.Primary" parent="Widget.Auxio.TextView.Item.Base">
        <item name="android:textAppearance">@style/TextAppearance.Auxio.TitleMedium
        </item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="Widget.Auxio.TextView.Item.Secondary" parent="Widget.Auxio.TextView.Item.Base">
        <item name="android:textAppearance">@style/TextAppearance.Auxio.BodyMedium</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="Widget.Auxio.TextView.Primary" parent="Widget.Auxio.TextView.Base">
        <item name="android:ellipsize">marquee</item>
        <item name="android:singleLine">true</item>
        <item name="android:marqueeRepeatLimit">marquee_forever</item>
        <item name="android:textAppearance">@style/TextAppearance.Auxio.TitleLarge</item>
    </style>

    <style name="Widget.Auxio.TextView.Secondary.Base" parent="Widget.Auxio.TextView.Base">
        <item name="android:textColor">?android:attr/textColorSecondary</item>
        <item name="android:textAppearance">@style/TextAppearance.Auxio.BodyLarge</item>
    </style>

    <style name="Widget.Auxio.TextView.Secondary.Ellipsize" parent="Widget.Auxio.TextView.Secondary.Base">
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>

    </style>

    <style name="Widget.Auxio.TextView.Secondary.Marquee" parent="Widget.Auxio.TextView.Secondary.Base">
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:marqueeRepeatLimit">marquee_forever</item>
    </style>

    <style name="Widget.Auxio.TextView.Primary.Compact" parent="Widget.Auxio.TextView.Base">
        <item name="android:ellipsize">marquee</item>
        <item name="android:singleLine">true</item>
        <item name="android:marqueeRepeatLimit">marquee_forever</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textAppearance">@style/TextAppearance.Auxio.LabelLarge</item>
    </style>

    <style name="Widget.Auxio.TextView.Secondary.Compact" parent="Widget.Auxio.TextView.Base">
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
        <item name="android:textAppearance">@style/TextAppearance.Auxio.BodySmall</item>
    </style>

    <style name="Widget.Auxio.TextView.Header" parent="Widget.Auxio.TextView.Base">
        <item name="android:paddingStart">@dimen/spacing_medium</item>
        <item name="android:paddingTop">@dimen/spacing_small</item>
        <item name="android:paddingBottom">@dimen/spacing_small</item>
        <item name="android:paddingEnd">@dimen/spacing_medium</item>
        <item name="android:minHeight">@dimen/size_touchable_small</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textColor">?attr/colorSecondary</item>
        <item name="android:textAppearance">@style/TextAppearance.Auxio.LabelLarge</item>
    </style>

    <style name="Widget.Auxio.TextView.Detail" parent="Widget.Auxio.TextView.Base">
        <item name="android:textAppearance">@style/TextAppearance.Auxio.TitleLarge</item>
        <item name="android:textAlignment">viewStart</item>
    </style>

    <style name="Widget.Auxio.TextView.Icon" parent="Widget.Auxio.TextView.Base">
        <item name="android:drawablePadding">@dimen/spacing_medium</item>
        <item name="android:paddingTop">@dimen/spacing_mid_medium</item>
        <item name="android:paddingStart">@dimen/spacing_medium</item>
        <item name="android:paddingBottom">@dimen/spacing_mid_medium</item>
        <item name="android:paddingEnd">@dimen/spacing_medium</item>
        <item name="android:textAppearance">@style/TextAppearance.Auxio.BodyLarge</item>
    </style>

    <style name="Widget.Auxio.TextView.Icon.Clickable" parent="Widget.Auxio.TextView.Icon">
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:background">?attr/selectableItemBackground</item>
    </style>

    <!-- BUTTON STYLES -->

    <style name="Widget.Auxio.Button.Primary" parent="Widget.Material3.Button">
        <item name="iconGravity">textStart</item>
        <item name="iconSize">@dimen/size_icon_small</item>
    </style>

    <style name="Widget.Auxio.Button.Secondary" parent="Widget.Material3.Button.TonalButton">
        <item name="iconGravity">textStart</item>
        <item name="iconSize">@dimen/size_icon_small</item>
    </style>

    <style name="Widget.Auxio.Button.Icon.Base" parent="Widget.Material3.Button.IconButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Auxio.UncheckableIconButton</item>
    </style>

    <style name="ThemeOverlay.Auxio.UncheckableIconButton" parent="">
        <item name="colorContainer">@android:color/transparent</item>
        <item name="colorOnContainer">?attr/colorOnSurfaceVariant</item>
    </style>

    <style name="Widget.Auxio.Button.Icon.Small" parent="Widget.Auxio.Button.Icon.Base">
        <item name="iconSize">@dimen/size_icon_small</item>
        <item name="android:minWidth">@dimen/size_touchable_small</item>
        <item name="android:minHeight">@dimen/size_touchable_small</item>
        <item name="android:insetTop">@dimen/spacing_tiny</item>
        <item name="android:insetBottom">@dimen/spacing_tiny</item>
        <item name="android:insetLeft">@dimen/spacing_tiny</item>
        <item name="android:insetRight">@dimen/spacing_tiny</item>
        <item name="android:paddingStart">@dimen/spacing_small</item>
        <item name="android:paddingEnd">@dimen/spacing_small</item>
        <item name="android:paddingTop">@dimen/spacing_small</item>
        <item name="android:paddingBottom">@dimen/spacing_small</item>
        <!-- Intentional to prevent button from spamming the log console -->
        <item name="iconTint" tools:ignore="PrivateResource">@color/m3_text_button_foreground_color_selector</item>
    </style>

    <style name="Widget.Auxio.Button.Icon.Medium" parent="Widget.Auxio.Button.Icon.Base">
        <item name="iconSize">@dimen/size_icon_medium</item>
        <item name="android:minWidth">@dimen/size_touchable_small</item>
        <item name="android:minHeight">@dimen/size_touchable_small</item>
        <item name="android:insetTop">@dimen/spacing_small</item>
        <item name="android:insetBottom">@dimen/spacing_small</item>
        <item name="android:insetLeft">0dp</item>
        <item name="android:insetRight">0dp</item>
        <item name="android:paddingStart">@dimen/spacing_small</item>
        <item name="android:paddingEnd">@dimen/spacing_small</item>
        <item name="android:paddingTop">@dimen/spacing_small</item>
        <item name="android:paddingBottom">@dimen/spacing_small</item>
        <item name="iconTint" tools:ignore="PrivateResource">@color/m3_text_button_foreground_color_selector</item>
    </style>

    <style name="Widget.Auxio.Button.Icon.Small.Primary" parent="Widget.Material3.Button.IconButton.Filled">
        <item name="iconSize">@dimen/size_icon_small</item>
        <item name="android:minWidth">@dimen/size_touchable_small</item>
        <item name="android:minHeight">@dimen/size_touchable_small</item>
        <item name="android:insetTop">@dimen/spacing_tiny</item>
        <item name="android:insetBottom">@dimen/spacing_tiny</item>
        <item name="android:insetLeft">@dimen/spacing_tiny</item>
        <item name="android:insetRight">@dimen/spacing_tiny</item>
        <item name="android:paddingStart">@dimen/spacing_small</item>
        <item name="android:paddingEnd">@dimen/spacing_small</item>
        <item name="android:paddingTop">@dimen/spacing_small</item>
        <item name="android:paddingBottom">@dimen/spacing_small</item>
    </style>

    <style name="Widget.Auxio.Button.Icon.Small.Secondary" parent="Widget.Material3.Button.IconButton.Filled.Tonal">
        <item name="iconSize">@dimen/size_icon_small</item>
        <item name="android:minWidth">@dimen/size_touchable_small</item>
        <item name="android:minHeight">@dimen/size_touchable_small</item>
        <item name="android:insetTop">@dimen/spacing_tiny</item>
        <item name="android:insetBottom">@dimen/spacing_tiny</item>
        <item name="android:insetLeft">@dimen/spacing_tiny</item>
        <item name="android:insetRight">@dimen/spacing_tiny</item>
        <item name="android:paddingStart">@dimen/spacing_small</item>
        <item name="android:paddingEnd">@dimen/spacing_small</item>
        <item name="android:paddingTop">@dimen/spacing_small</item>
        <item name="android:paddingBottom">@dimen/spacing_small</item>
    </style>

    <style name="Widget.Auxio.Button.Icon.Small.PlayPause" parent="Widget.Auxio.Button.Icon.Small">
        <item name="shapeAppearanceOverlay">
            @style/ShapeAppearanceOverlay.Material3.FloatingActionButton
        </item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Auxio.IconButton.PlayPause</item>
    </style>


    <style name="Widget.Auxio.Button.Icon.Huge" parent="Widget.Material3.Button.IconButton">
        <item name="iconSize">@dimen/size_icon_large</item>
        <item name="android:minWidth">@dimen/size_touchable_medium</item>
        <item name="android:minHeight">@dimen/size_touchable_medium</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:insetLeft">0dp</item>
        <item name="android:insetRight">0dp</item>
        <item name="android:paddingStart">8dp</item>
        <item name="android:paddingEnd">8dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Auxio.IconButton.Palette.Secondary</item>
    </style>

    <style name="ThemeOverlay.Auxio.IconButton.Palette.Secondary" parent="">
        <item name="colorContainer">?attr/colorSecondaryContainer</item>
        <item name="colorOnContainer">?attr/colorOnSecondaryContainer</item>
    </style>

    <style name="Widget.Auxio.Button.PlayPause" parent="Widget.Material3.Button.IconButton.Filled.Tonal">
        <item name="android:minWidth">@dimen/size_touchable_huge</item>
        <item name="android:minHeight">@dimen/size_touchable_huge</item>
        <item name="iconSize">@dimen/size_icon_huge</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:insetLeft">0dp</item>
        <item name="android:insetRight">0dp</item>
        <item name="android:paddingStart">@dimen/spacing_medium</item>
        <item name="android:paddingEnd">@dimen/spacing_medium</item>
        <item name="android:paddingTop">@dimen/spacing_medium</item>
        <item name="android:paddingBottom">@dimen/spacing_medium</item>
        <item name="shapeAppearanceOverlay">
            @style/ShapeAppearanceOverlay.Material3.FloatingActionButton
        </item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Auxio.IconButton.PlayPause</item>
    </style>

    <style name="ThemeOverlay.Auxio.IconButton.PlayPause" parent="">
        <item name="colorContainer">?attr/colorSecondary</item>
        <item name="colorOnContainer">?attr/colorOnSecondary</item>
    </style>
</resources>