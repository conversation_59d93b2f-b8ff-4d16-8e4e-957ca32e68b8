<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="androidx.media3.demo.main">

  <uses-permission android:name="android.permission.INTERNET"/>
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
  <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
  <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC"/>
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

  <uses-feature android:name="android.software.leanback" android:required="false"/>
  <uses-feature android:name="android.hardware.touchscreen" android:required="false"/>
  <uses-sdk/>

  <application
      android:label="@string/application_name"
      android:icon="@mipmap/ic_launcher"
      android:banner="@drawable/ic_banner"
      android:largeHeap="true"
      android:allowBackup="false"
      android:supportsRtl="true"
      tools:targetApi="29">

    <activity android:name=".SampleChooserActivity"
        android:configChanges="keyboardHidden"
        android:label="@string/application_name"
        android:theme="@style/Theme.AppCompat"
        android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN"/>
        <category android:name="android.intent.category.LAUNCHER"/>
        <category android:name="android.intent.category.LEANBACK_LAUNCHER"/>
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <action android:name="androidx.media3.demo.main.action.BROWSE"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:scheme="http"/>
        <data android:scheme="https"/>
        <data android:scheme="content"/>
        <data android:scheme="asset"/>
        <data android:scheme="file"/>
        <data android:host="*"/>
        <data android:pathPattern=".*\\.exolist\\.json"/>
      </intent-filter>
    </activity>

    <activity android:name=".PlayerActivity"
        android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
        android:launchMode="singleTop"
        android:label="@string/application_name"
        android:theme="@style/PlayerTheme"
        android:exported="true">
      <intent-filter>
        <action android:name="androidx.media3.demo.main.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <data android:scheme="http"/>
        <data android:scheme="https"/>
        <data android:scheme="content"/>
        <data android:scheme="asset"/>
        <data android:scheme="file"/>
        <data android:scheme="ssai"/>
      </intent-filter>
      <intent-filter>
        <action android:name="androidx.media3.demo.main.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <data android:scheme="content"/>
        <data android:mimeType="*/*"/>
      </intent-filter>
      <intent-filter>
        <action android:name="androidx.media3.demo.main.action.VIEW_LIST"/>
        <category android:name="android.intent.category.DEFAULT"/>
      </intent-filter>
    </activity>

    <service android:name=".DemoDownloadService"
        android:exported="false"
        android:foregroundServiceType="dataSync">
      <intent-filter>
        <action android:name="androidx.media3.exoplayer.downloadService.action.RESTART"/>
        <category android:name="android.intent.category.DEFAULT"/>
      </intent-filter>
    </service>

    <service android:name="androidx.media3.exoplayer.scheduler.PlatformScheduler$PlatformSchedulerService"
        android:permission="android.permission.BIND_JOB_SERVICE"
        android:exported="true"/>

  </application>

</manifest>
