<?xml version="1.0" encoding="utf-8"?>
<android.widget.RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@android:id/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:theme="@style/Theme.Auxio.Widget">

    <!--
    For our widgets to work, we need to scale the ImageView across a 1:1 aspect ratio.
    However, since we are working with a RemoteViews instance, we can't just use ConstraintLayout
    to achieve this. We can use RelativeLayout, but there's no way to force an aspect ratio with
    that layout. However, if we create an invisible ImageView that contains a massive fixed-size
    drawable and then clamp our main ImageView to it, we can make the view scale on a 1:1 aspect
    ratio.

    This is easily one of the worst layout hacks I've done, but it seems to work.
    -->

    <android.widget.ImageView
        android:id="@+id/widget_aspect_ratio"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="@dimen/spacing_small"
        android:layout_marginTop="@dimen/spacing_small"
        android:layout_marginEnd="@dimen/spacing_small"
        android:layout_marginBottom="@dimen/spacing_small"
        android:adjustViewBounds="true"
        android:scaleType="fitCenter"
        android:src="@drawable/ui_remote_aspect_ratio"
        android:visibility="invisible"
        tools:ignore="ContentDescription" />

    <android.widget.ImageView
        android:id="@+id/widget_cover"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_alignStart="@id/widget_aspect_ratio"
        android:layout_alignTop="@id/widget_aspect_ratio"
        android:layout_alignEnd="@id/widget_aspect_ratio"
        android:layout_alignBottom="@id/widget_aspect_ratio"
        android:src="@drawable/ic_remote_default_cover_24"
        tools:ignore="ContentDescription" />

    <!--
    We want to evenly space these buttons, but using a plain layout_weight system would result
    in more padding being applied to the button icons than desired. Fix this by adding empty
    views that respond to layout_weight and space out the fixed-size buttons.

    As for why we use ImageViews, it is the only blank view that the AppWidget API will accept.
    Because of course it is.
    -->
    <android.widget.LinearLayout
        android:id="@+id/widget_controls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center"
        android:background="@drawable/ui_widget_bg_round"
        android:backgroundTint="?attr/colorSurface"
        android:orientation="horizontal"
        android:padding="@dimen/spacing_mid_medium">

        <android.widget.ImageButton
            android:id="@+id/widget_skip_prev"
            style="@style/Widget.Auxio.MaterialButton.AppWidget"
            android:layout_width="@dimen/size_touchable_small"
            android:layout_height="@dimen/size_touchable_small"
            android:contentDescription="@string/desc_skip_prev"
            android:src="@drawable/ic_skip_prev_24" />

        <android.widget.ImageView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <android.widget.ImageButton
            android:id="@+id/widget_play_pause"
            style="@style/Widget.Auxio.MaterialButton.AppWidget.PlayPause"
            android:layout_width="@dimen/size_touchable_small"
            android:layout_height="@dimen/size_touchable_small"
            android:contentDescription="@string/desc_play_pause"
            android:src="@drawable/ic_play_24" />

        <android.widget.ImageView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <android.widget.ImageButton
            android:id="@+id/widget_skip_next"
            style="@style/Widget.Auxio.MaterialButton.AppWidget"
            android:layout_width="@dimen/size_touchable_small"
            android:layout_height="@dimen/size_touchable_small"
            android:contentDescription="@string/desc_skip_next"
            android:src="@drawable/ic_skip_next_24" />

    </android.widget.LinearLayout>
</android.widget.RelativeLayout>


