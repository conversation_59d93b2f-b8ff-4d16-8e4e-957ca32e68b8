<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/new_location_label"
    style="@style/Widget.Auxio.TextView.Icon.Clickable"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:text="@string/set_locations_new"
    android:paddingTop="@dimen/spacing_mid_large"
    android:paddingBottom="@dimen/spacing_mid_large"
    android:paddingStart="@dimen/spacing_mid_large"
    android:paddingEnd="@dimen/spacing_large"
    app:drawableStartCompat="@drawable/ic_add_24"
    app:layout_constraintBottom_toTopOf="@+id/about_wiki"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@+id/about_version" />
