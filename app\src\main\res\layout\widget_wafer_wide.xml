<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@android:id/background"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/ui_widget_bg_sharp"
    android:backgroundTint="?attr/colorSurface"
    android:baselineAligned="false"
    android:orientation="horizontal"
    android:theme="@style/Theme.Auxio.Widget">

    <!--
    clipToOutline won't actually do anything before Android 12, but that's fine since we won't
    show a cover then anyway.
    -->
    <android.widget.ImageView
        android:id="@+id/widget_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:background="@drawable/ui_widget_bg_round"
        tools:ignore="ContentDescription,UnusedAttribute" />

    <android.widget.LinearLayout
        android:id="@+id/widget_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:padding="@dimen/spacing_mid_medium"
        android:orientation="horizontal">

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ui_widget_circle_button_bg">

            <android.widget.ImageButton
                android:id="@+id/widget_repeat"
                style="@style/Widget.Auxio.MaterialButton.AppWidget"
                android:layout_width="@dimen/size_touchable_small"
                android:layout_height="@dimen/size_touchable_small"
                android:contentDescription="@string/desc_change_repeat"
                android:src="@drawable/ic_repeat_off_24" />


        </FrameLayout>

        <android.widget.ImageView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ui_widget_circle_button_bg">

            <android.widget.ImageButton
                android:id="@+id/widget_skip_prev"
                style="@style/Widget.Auxio.MaterialButton.AppWidget"
                android:layout_width="@dimen/size_touchable_small"
                android:layout_height="@dimen/size_touchable_small"
                android:contentDescription="@string/desc_skip_prev"
                android:src="@drawable/ic_skip_prev_24" />
        </FrameLayout>


        <android.widget.ImageView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <android.widget.ImageButton
            android:id="@+id/widget_play_pause"
            style="@style/Widget.Auxio.MaterialButton.AppWidget.PlayPause"
            android:layout_width="@dimen/size_touchable_small"
            android:layout_height="@dimen/size_touchable_small"
            android:contentDescription="@string/desc_play_pause"
            android:src="@drawable/ic_play_24" />

        <android.widget.ImageView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ui_widget_circle_button_bg">

            <android.widget.ImageButton
                android:id="@+id/widget_skip_next"
                style="@style/Widget.Auxio.MaterialButton.AppWidget"
                android:layout_width="@dimen/size_touchable_small"
                android:layout_height="@dimen/size_touchable_small"
                android:contentDescription="@string/desc_skip_next"
                android:src="@drawable/ic_skip_next_24" />

        </FrameLayout>


        <android.widget.ImageView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1" />

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ui_widget_circle_button_bg">

            <android.widget.ImageButton
                android:id="@+id/widget_shuffle"
                style="@style/Widget.Auxio.MaterialButton.AppWidget"
                android:layout_width="@dimen/size_touchable_small"
                android:layout_height="@dimen/size_touchable_small"
                android:contentDescription="@string/desc_shuffle"
                android:src="@drawable/ic_shuffle_off_24" />


        </FrameLayout>


    </android.widget.LinearLayout>
</FrameLayout>


