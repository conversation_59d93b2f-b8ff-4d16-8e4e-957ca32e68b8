<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2024 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<androidx.constraintlayout.widget.ConstraintLayout
  xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:padding="16dp">

  <com.google.android.material.card.MaterialCardView
    android:id="@+id/composition_preview_card_view"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_weight="1"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:cardCornerRadius="4dp"
    app:cardElevation="2dp">

    <LinearLayout
      android:orientation="vertical"
      android:layout_width="match_parent"
      android:layout_height="wrap_content" >

      <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/input_text_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:padding="8dp"
        android:textAppearance="@style/TextAppearance.AppCompat.Medium"
        android:text="@string/preview_composition" />

      <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="200dp" >

        <androidx.media3.ui.PlayerView
          android:id="@+id/composition_player_view"
          android:layout_width="match_parent"
          android:layout_height="wrap_content" />

      </FrameLayout>

    </LinearLayout>

  </com.google.android.material.card.MaterialCardView>

  <androidx.appcompat.widget.AppCompatTextView
    android:id="@+id/sequence_header_text"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:text="@string/video_sequence_items"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/composition_preview_card_view"
    app:layout_constraintBottom_toTopOf="@id/composition_preset_list"/>

  <androidx.appcompat.widget.AppCompatButton
    android:id="@+id/edit_sequence_button"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:textAppearance="@style/TextAppearance.AppCompat.Small"
    android:text="@string/edit"
    app:layout_constraintStart_toEndOf="@id/sequence_header_text"
    app:layout_constraintTop_toTopOf="@id/sequence_header_text"
    app:layout_constraintBottom_toBottomOf="@id/sequence_header_text"/>

  <androidx.appcompat.widget.AppCompatCheckBox
    android:id="@+id/apply_video_effects_checkbox"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:text="@string/add_effects"
    app:layout_constraintStart_toEndOf="@id/edit_sequence_button"
    app:layout_constraintTop_toTopOf="@id/sequence_header_text"
    app:layout_constraintBottom_toBottomOf="@id/sequence_header_text" />

  <androidx.recyclerview.widget.RecyclerView
    android:id="@+id/composition_preset_list"
    android:layout_width="match_parent"
    android:layout_height="0dp"
    android:layout_marginTop="16dp"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@id/edit_sequence_button"
    app:layout_constraintBottom_toTopOf="@id/export_information_text"/>

  <androidx.appcompat.widget.AppCompatTextView
    android:id="@+id/export_information_text"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintBottom_toTopOf="@id/background_audio_checkbox"/>

  <androidx.appcompat.widget.AppCompatCheckBox
    android:id="@+id/background_audio_checkbox"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:text="@string/add_background_audio"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintBottom_toTopOf="@id/resolution_height_setting" />

  <LinearLayout
    android:id="@+id/resolution_height_setting"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:layout_marginBottom="8dp"
    app:layout_constraintBottom_toTopOf="@id/hdr_mode_setting">
    <TextView
      android:layout_height="wrap_content"
      android:layout_width="0dp"
      android:layout_weight="1"
      android:text="@string/output_video_resolution"/>
    <Spinner
      android:id="@+id/resolution_height_spinner"
      android:layout_gravity="end|center_vertical"
      android:gravity="end"
      android:layout_height="wrap_content"
      android:layout_width="wrap_content"/>
  </LinearLayout>

  <LinearLayout
    android:id="@+id/hdr_mode_setting"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:layout_marginBottom="12dp"
    app:layout_constraintBottom_toTopOf="@id/preview_button">
    <TextView
      android:layout_height="wrap_content"
      android:layout_width="0dp"
      android:layout_weight="1"
      android:text="@string/hdr_mode" />
    <Spinner
      android:id="@+id/hdr_mode_spinner"
      android:layout_gravity="end|center_vertical"
      android:gravity="end"
      android:layout_height="wrap_content"
      android:layout_width="wrap_content"/>
  </LinearLayout>

  <androidx.appcompat.widget.AppCompatButton
    android:id="@+id/composition_export_button"
    android:text="@string/export"
    android:layout_marginTop="16dp"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:layout_constraintStart_toEndOf="@id/preview_button"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"/>

  <androidx.appcompat.widget.AppCompatButton
    android:id="@+id/preview_button"
    android:text="@string/preview"
    android:layout_marginTop="16dp"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintEnd_toStartOf="@id/composition_export_button"
    app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
