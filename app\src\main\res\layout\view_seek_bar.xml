<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.slider.Slider
        android:id="@+id/seek_bar_slider"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/spacing_medium"
        android:valueFrom="0"
        android:valueTo="1" />

    <TextView
        android:id="@+id/seek_bar_position"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|start"
        android:layout_marginStart="@dimen/spacing_medium"
        android:layout_marginBottom="@dimen/spacing_tiny"
        android:textAppearance="@style/TextAppearance.Auxio.BodySmall"
        android:textColor="@color/sel_activatable_text_secondary"
        tools:text="11:38" />

    <TextView
        android:id="@+id/seek_bar_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="@dimen/spacing_medium"
        android:layout_marginBottom="@dimen/spacing_tiny"
        android:textAppearance="@style/TextAppearance.Auxio.BodySmall"
        android:textColor="?android:attr/textColorSecondary"
        tools:text="16:16" />

</FrameLayout>