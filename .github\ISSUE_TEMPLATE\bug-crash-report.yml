name: Bug/Crash Report
description: Report a problem
labels: ["bug"]
assignees:
  - OxygenCobalt
body:
  - type: markdown
    attributes:
      value: |
        Welcome to Auxio's bug report form.
        Please note that not every reported issue can be fixed. Well-written bug reports are more likely to be resolved.
  - type: textarea
    id: desc
    attributes:
      label: Describe the Bug/Crash
      description: Provide a clear and concise description of the issue alongside steps to reproduce it.
      placeholder: |
        1. Go to X
        2. Click on Y
        3. Scroll down to Z
        4. See error
    validations:
      required: true
  - type: textarea
    id: intended
    attributes:
      label: Describe the intended behavior
      description: Provide a clear and concise descripton of the correct behavior. Include examples from other music players if applicable.
      placeholder: Should do X.
    validations:
      required: true
  - type: dropdown
    id: android-version
    attributes:
      label: What android version do you use?
      options:
        - Android 15
        - Android 14
        - Android 13
        - Android 12L
        - Android 12
        - Android 11
        - Android 10
        - Android 9 (Pie)
        - Android 8.1 (Oreo)
        - Android 8 (Oreo)
        - Android 7 (Nougat)
        - Android 6 (Marshmallow)
        - Android 5.1 (<PERSON>llipop)
        - Android 5 (<PERSON>llipop)
    validations:
      required: true
  - type: textarea
    id: devide-model
    attributes:
      label: What device model do you use?
      description: Include details on OEM Skin or Custom ROM if possible.
      placeholder: OnePlus 7T (LineageOS)
    validations:
      required: true
  - type: textarea
    id: sample-file
    attributes:
      label: Provide a sample file
      description: Upload a sample file the error is related to the loading or playback of music files. **IF YOU DO NOT DO THIS, I WILL BE UNABLE TO SOLVE YOUR ISSUE.** Music loading errors may indicate what file is causing the issue. Upload that file. If the audio is copyrighted, you should cut it out in an audio error while still making sure the edited file reproduces the issue. *Upload a ZIP file containing the files or share a link to a file hosted on the cloud.*
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Bug report
      description: |
        If possible, provide a "bug report" ZIP file to make it easier to identify the issue. Go to [here](https://developer.android.com/studio/debug/bug-report) for guidance on how to take one.
    validations:
      required: true
  - type: checkboxes
    id: terms
    attributes:
      label: Duplicates
      description: By submitting this issue, you aknowledge the following
      options:
        - label: I have checked the [Troubleshooting](https://github.com/OxygenCobalt/Auxio/wiki/Troubleshooting) page.
          required: true
        - label: I have checked this issue for duplicates.
          required: true
        - label: I have checked that this issue occurs on the [lastest version](https://github.com/OxygenCobalt/Auxio/releases).
          required: true
        - label: I agree to the [Contribution Guidelines](https://github.com/OxygenCobalt/Auxio/blob/dev/.github/CONTRIBUTING.md).
          required: true          
