<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  tools:context=".MainActivity">

  <ListView
    android:id="@+id/media_list_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
  </ListView>

  <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
    android:id="@+id/open_player_floating_button"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom|center_horizontal"
    android:textColor="@color/white"
    android:layout_margin="10dp"
    app:icon="@drawable/exo_icon_play"
    app:iconTint="@color/white"
    app:backgroundTint="@color/purple_500"
    android:text="@string/current_playlist_name"
    android:contentDescription="@string/open_player_content_description" />

</FrameLayout>
