<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/spacing_tiny"
    android:theme="@style/ThemeOverlay.Accent">

    <org.oxycblt.auxio.ui.RippleFixMaterialButton
        android:id="@+id/accent"
        style="@style/Widget.Auxio.Button.Icon.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:icon="@drawable/ic_check_24"
        app:iconTint="?attr/colorSurface"
        tools:backgroundTint="?attr/colorPrimary"
        tools:ignore="ContentDescription, SpeakableTextPresentCheck" />

</FrameLayout>
