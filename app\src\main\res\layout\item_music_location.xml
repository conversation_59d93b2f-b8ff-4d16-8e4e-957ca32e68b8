<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingTop="@dimen/spacing_small"
    android:paddingBottom="@dimen/spacing_small">

    <TextView
        android:id="@+id/location_path"
        style="@style/Widget.Auxio.TextView.Item.Primary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/spacing_large"
        android:layout_marginEnd="@dimen/spacing_mid_medium"
        android:layout_weight="1"
        android:gravity="center"
        android:maxLines="@null"
        android:textAppearance="@style/TextAppearance.Auxio.BodyLarge"
        tools:text="primary:path/to/music" />

    <org.oxycblt.auxio.ui.RippleFixMaterialButton
        android:id="@+id/location_delete"
        style="@style/Widget.Auxio.Button.Icon.Small"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginEnd="@dimen/spacing_mid_large"
        android:contentDescription="@string/desc_music_location_delete"
        app:icon="@drawable/ic_delete_24"
        app:tint="?attr/colorControlNormal" />

</LinearLayout>
