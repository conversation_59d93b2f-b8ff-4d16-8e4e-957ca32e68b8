<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.textfield.TextInputLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/property_name"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/spacing_medium"
    android:paddingTop="@dimen/spacing_small"
    android:paddingEnd="@dimen/spacing_medium"
    android:paddingBottom="@dimen/spacing_small"
    app:expandedHintEnabled="false"
    tools:hint="@string/lbl_path">

    <org.oxycblt.auxio.detail.ReadOnlyTextInput
        android:id="@+id/property_value"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAlignment="viewStart"
        tools:text="file.mp3" />

</com.google.android.material.textfield.TextInputLayout>
