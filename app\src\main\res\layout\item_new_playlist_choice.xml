<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:paddingStart="@dimen/spacing_large"
    android:paddingTop="@dimen/spacing_mid_medium"
    android:paddingEnd="@dimen/spacing_large"
    android:paddingBottom="@dimen/spacing_mid_medium">

    <org.oxycblt.auxio.image.CoverView
        android:id="@+id/picker_image"
        style="@style/Widget.Auxio.Image.Small"
        android:contentDescription="@string/lbl_new_playlist"
        app:enablePlaybackIndicator="false"
        app:enableSelectionBadge="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/picker_icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="center"
            android:src="@drawable/ic_add_24"
            app:tint="@color/sel_on_cover_bg"
            tools:ignore="ContentDescription" />

    </org.oxycblt.auxio.image.CoverView>

    <TextView
        android:id="@+id/picker_name"
        style="@style/Widget.Auxio.TextView.Item.Primary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/spacing_mid_medium"
        android:text="@string/lbl_new_playlist"
        android:textColor="@color/sel_selectable_text_primary"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/picker_image"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

</androidx.constraintlayout.widget.ConstraintLayout>
