Auxio は、高速で信頼性の高い UI/UX を備えたローカル ミュージック プレーヤーであり、他のミュージック プレーヤーに見られる多くの無駄な機能はありません。 Exoplayer から構築された Auxio は、時代遅れの Android 機能を使用する他のアプリと比較して、優れたライブラリ サポートとリスニング品質を備えています。 つまり、<b>音楽を再生します</b>。

<b>特徴</b>

- ExoPlayer ベースの再生
- 最新のマテリアル・デザイン・ガイドラインに基づいたスマートなUI
- 特殊なケースよりも使いやすさを優先する独自の UX
- カスタマイズ可能な動作
- ディスク番号、複数のアーティスト、リリース タイプ、
正確な/元の日付、並べ替えタグなど
- アーティストとアルバムアーティストを統合した高度なアーティストシステム
- SD カードを意識したフォルダ管理
- 信頼できる再生状態の永続性
- ReplayGain の完全サポート (MP3、FLAC、OGG、OPUS、および MP4 ファイル)
- 外部イコライザーのサポート (例: Wavelet)
- 隅々まで
- 埋め込みカバーのサポート
- 検索機能
- ヘッドセットの自動再生
- サイズに自動的に適応するスタイリッシュなウィジェット
- 完全にプライベートでオフライン
- 丸みを帯びたアルバム カバーはありません (お望みであれば)
