// Copyright 2023 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
apply from: '../../constants.gradle'
apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    namespace 'androidx.media3.demo.session.automotive'

    compileSdk project.ext.compileSdkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    defaultConfig {
        versionName project.ext.releaseVersion
        versionCode project.ext.releaseVersionCode
        minSdkVersion project.ext.automotiveMinSdkVersion
        targetSdkVersion project.ext.appTargetSdkVersion
    }

    buildTypes {
        release {
            shrinkResources true
            minifyEnabled true
            proguardFiles = [
                getDefaultProguardFile('proguard-android-optimize.txt')
            ]
            signingConfig signingConfigs.debug
        }
        debug {
            jniDebuggable = true
        }
    }

    lintOptions {
        // The demo app isn't indexed, and doesn't have translations.
        disable 'GoogleAppIndexingWarning','MissingTranslation'
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:' + androidxCoreVersion
    implementation 'androidx.appcompat:appcompat:' + androidxAppCompatVersion
    implementation 'com.google.android.material:material:' + androidxMaterialVersion
    implementation project(modulePrefix + 'lib-session')
    implementation project(modulePrefix + 'demo-session-service')
}
