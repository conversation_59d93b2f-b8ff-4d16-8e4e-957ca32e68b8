// Copyright 2021 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
apply from: '../../constants.gradle'
apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    namespace 'androidx.media3.demo.session'

    compileSdk project.ext.compileSdkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    defaultConfig {
        versionName project.ext.releaseVersion
        versionCode project.ext.releaseVersionCode
        minSdkVersion project.ext.minSdkVersion
        targetSdkVersion project.ext.appTargetSdkVersion
    }

    buildTypes {
        release {
            shrinkResources true
            minifyEnabled true
            proguardFiles = [
                'proguard-rules.txt',
                getDefaultProguardFile('proguard-android-optimize.txt')
            ]
            signingConfig signingConfigs.debug
        }
        debug {
            jniDebuggable = true
        }
    }

    lintOptions {
        // The demo app isn't indexed, and doesn't have translations.
        disable 'GoogleAppIndexingWarning','MissingTranslation'
    }
}

dependencies {
    // For detecting and debugging leaks only. LeakCanary is not needed for demo app to work.
    debugImplementation 'com.squareup.leakcanary:leakcanary-android:' + leakCanaryVersion
    implementation 'androidx.core:core-ktx:' + androidxCoreVersion
    implementation 'androidx.lifecycle:lifecycle-common:' + androidxLifecycleVersion
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:' + androidxLifecycleVersion
    implementation 'androidx.appcompat:appcompat:' + androidxAppCompatVersion
    implementation 'com.google.android.material:material:' + androidxMaterialVersion
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-guava:' + kotlinxCoroutinesVersion
    implementation project(modulePrefix + 'lib-ui')
    implementation project(modulePrefix + 'lib-session')
    implementation project(modulePrefix + 'demo-session-service')
}
