<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="red_primary">#904A42</color>
    <color name="red_onPrimary">#FFFFFF</color>
    <color name="red_primaryContainer">#FFDAD5</color>
    <color name="red_onPrimaryContainer">#3B0906</color>
    <color name="red_secondary">#775652</color>
    <color name="red_onSecondary">#FFFFFF</color>
    <color name="red_secondaryContainer">#FFDAD5</color>
    <color name="red_onSecondaryContainer">#2C1512</color>
    <color name="red_tertiary">#705C2E</color>
    <color name="red_onTertiary">#FFFFFF</color>
    <color name="red_tertiaryContainer">#FCDFA6</color>
    <color name="red_onTertiaryContainer">#261A00</color>
    <color name="red_error">#BA1A1A</color>
    <color name="red_onError">#FFFFFF</color>
    <color name="red_errorContainer">#FFDAD6</color>
    <color name="red_onErrorContainer">#410002</color>
    <color name="red_background">#FFF8F7</color>
    <color name="red_onBackground">#231918</color>
    <color name="red_surface">#FFF8F7</color>
    <color name="red_onSurface">#231918</color>
    <color name="red_surfaceVariant">#F5DDDA</color>
    <color name="red_onSurfaceVariant">#534341</color>
    <color name="red_outline">#857370</color>
    <color name="red_outlineVariant">#D8C2BE</color>
    <color name="red_inverseSurface">#392E2C</color>
    <color name="red_inverseOnSurface">#FFEDEA</color>
    <color name="red_inversePrimary">#FFB4A9</color>
    <color name="red_primaryFixed">#FFDAD5</color>
    <color name="red_onPrimaryFixed">#3B0906</color>
    <color name="red_primaryFixedDim">#FFB4A9</color>
    <color name="red_onPrimaryFixedVariant">#73342C</color>
    <color name="red_secondaryFixed">#FFDAD5</color>
    <color name="red_onSecondaryFixed">#2C1512</color>
    <color name="red_secondaryFixedDim">#E7BDB7</color>
    <color name="red_onSecondaryFixedVariant">#5D3F3B</color>
    <color name="red_tertiaryFixed">#FCDFA6</color>
    <color name="red_onTertiaryFixed">#261A00</color>
    <color name="red_tertiaryFixedDim">#DFC38C</color>
    <color name="red_onTertiaryFixedVariant">#574419</color>
    <color name="red_surfaceDim">#E8D6D3</color>
    <color name="red_surfaceBright">#FFF8F7</color>
    <color name="red_surfaceContainerLowest">#FFFFFF</color>
    <color name="red_surfaceContainerLow">#FFF0EE</color>
    <color name="red_surfaceContainer">#FCEAE7</color>
    <color name="red_surfaceContainerHigh">#F7E4E1</color>
    <color name="red_surfaceContainerHighest">#F1DEDC</color>

    <color name="pink_primary">#8E4956</color>
    <color name="pink_onPrimary">#FFFFFF</color>
    <color name="pink_primaryContainer">#FFD9DD</color>
    <color name="pink_onPrimaryContainer">#3B0715</color>
    <color name="pink_secondary">#76565A</color>
    <color name="pink_onSecondary">#FFFFFF</color>
    <color name="pink_secondaryContainer">#FFD9DD</color>
    <color name="pink_onSecondaryContainer">#2C1519</color>
    <color name="pink_tertiary">#795831</color>
    <color name="pink_onTertiary">#FFFFFF</color>
    <color name="pink_tertiaryContainer">#FFDDB9</color>
    <color name="pink_onTertiaryContainer">#2B1700</color>
    <color name="pink_error">#BA1A1A</color>
    <color name="pink_onError">#FFFFFF</color>
    <color name="pink_errorContainer">#FFDAD6</color>
    <color name="pink_onErrorContainer">#410002</color>
    <color name="pink_background">#FFF8F7</color>
    <color name="pink_onBackground">#22191A</color>
    <color name="pink_surface">#FFF8F7</color>
    <color name="pink_onSurface">#22191A</color>
    <color name="pink_surfaceVariant">#F3DDDF</color>
    <color name="pink_onSurfaceVariant">#524345</color>
    <color name="pink_outline">#847375</color>
    <color name="pink_outlineVariant">#D6C2C3</color>
    <color name="pink_inverseSurface">#382E2F</color>
    <color name="pink_inverseOnSurface">#FEEDEE</color>
    <color name="pink_inversePrimary">#FFB2BD</color>
    <color name="pink_primaryFixed">#FFD9DD</color>
    <color name="pink_onPrimaryFixed">#3B0715</color>
    <color name="pink_primaryFixedDim">#FFB2BD</color>
    <color name="pink_onPrimaryFixedVariant">#72333F</color>
    <color name="pink_secondaryFixed">#FFD9DD</color>
    <color name="pink_onSecondaryFixed">#2C1519</color>
    <color name="pink_secondaryFixedDim">#E5BDC1</color>
    <color name="pink_onSecondaryFixedVariant">#5C3F43</color>
    <color name="pink_tertiaryFixed">#FFDDB9</color>
    <color name="pink_onTertiaryFixed">#2B1700</color>
    <color name="pink_tertiaryFixedDim">#EABF8F</color>
    <color name="pink_onTertiaryFixedVariant">#5E411C</color>
    <color name="pink_surfaceDim">#E7D6D7</color>
    <color name="pink_surfaceBright">#FFF8F7</color>
    <color name="pink_surfaceContainerLowest">#FFFFFF</color>
    <color name="pink_surfaceContainerLow">#FFF0F1</color>
    <color name="pink_surfaceContainer">#FBEAEB</color>
    <color name="pink_surfaceContainerHigh">#F6E4E5</color>
    <color name="pink_surfaceContainerHighest">#F0DEDF</color>

    <color name="purple_primary">#7B4E7F</color>
    <color name="purple_onPrimary">#FFFFFF</color>
    <color name="purple_primaryContainer">#FFD6FE</color>
    <color name="purple_onPrimaryContainer">#310937</color>
    <color name="purple_secondary">#6B586B</color>
    <color name="purple_onSecondary">#FFFFFF</color>
    <color name="purple_secondaryContainer">#F4DBF1</color>
    <color name="purple_onSecondaryContainer">#251626</color>
    <color name="purple_tertiary">#82524A</color>
    <color name="purple_onTertiary">#FFFFFF</color>
    <color name="purple_tertiaryContainer">#FFDAD4</color>
    <color name="purple_onTertiaryContainer">#33110C</color>
    <color name="purple_error">#BA1A1A</color>
    <color name="purple_onError">#FFFFFF</color>
    <color name="purple_errorContainer">#FFDAD6</color>
    <color name="purple_onErrorContainer">#410002</color>
    <color name="purple_background">#FFF7FA</color>
    <color name="purple_onBackground">#1F1A1F</color>
    <color name="purple_surface">#FFF7FA</color>
    <color name="purple_onSurface">#1F1A1F</color>
    <color name="purple_surfaceVariant">#ECDFE8</color>
    <color name="purple_onSurfaceVariant">#4D444C</color>
    <color name="purple_outline">#7F747D</color>
    <color name="purple_outlineVariant">#D0C3CC</color>
    <color name="purple_inverseSurface">#352F34</color>
    <color name="purple_inverseOnSurface">#F9EEF5</color>
    <color name="purple_inversePrimary">#EBB5ED</color>
    <color name="purple_primaryFixed">#FFD6FE</color>
    <color name="purple_onPrimaryFixed">#310937</color>
    <color name="purple_primaryFixedDim">#EBB5ED</color>
    <color name="purple_onPrimaryFixedVariant">#613766</color>
    <color name="purple_secondaryFixed">#F4DBF1</color>
    <color name="purple_onSecondaryFixed">#251626</color>
    <color name="purple_secondaryFixedDim">#D7BFD5</color>
    <color name="purple_onSecondaryFixedVariant">#534153</color>
    <color name="purple_tertiaryFixed">#FFDAD4</color>
    <color name="purple_onTertiaryFixed">#33110C</color>
    <color name="purple_tertiaryFixedDim">#F6B8AD</color>
    <color name="purple_onTertiaryFixedVariant">#673B34</color>
    <color name="purple_surfaceDim">#E2D7DE</color>
    <color name="purple_surfaceBright">#FFF7FA</color>
    <color name="purple_surfaceContainerLowest">#FFFFFF</color>
    <color name="purple_surfaceContainerLow">#FCF0F7</color>
    <color name="purple_surfaceContainer">#F6EBF2</color>
    <color name="purple_surfaceContainerHigh">#F0E5EC</color>
    <color name="purple_surfaceContainerHighest">#EBDFE6</color>

    <color name="deep_purple_primary">#68548E</color>
    <color name="deep_purple_onPrimary">#FFFFFF</color>
    <color name="deep_purple_primaryContainer">#EBDDFF</color>
    <color name="deep_purple_onPrimaryContainer">#230F46</color>
    <color name="deep_purple_secondary">#635B70</color>
    <color name="deep_purple_onSecondary">#FFFFFF</color>
    <color name="deep_purple_secondaryContainer">#E9DEF8</color>
    <color name="deep_purple_onSecondaryContainer">#1F182B</color>
    <color name="deep_purple_tertiary">#7E525D</color>
    <color name="deep_purple_onTertiary">#FFFFFF</color>
    <color name="deep_purple_tertiaryContainer">#FFD9E1</color>
    <color name="deep_purple_onTertiaryContainer">#31101B</color>
    <color name="deep_purple_error">#BA1A1A</color>
    <color name="deep_purple_onError">#FFFFFF</color>
    <color name="deep_purple_errorContainer">#FFDAD6</color>
    <color name="deep_purple_onErrorContainer">#410002</color>
    <color name="deep_purple_background">#FEF7FF</color>
    <color name="deep_purple_onBackground">#1D1B20</color>
    <color name="deep_purple_surface">#FEF7FF</color>
    <color name="deep_purple_onSurface">#1D1B20</color>
    <color name="deep_purple_surfaceVariant">#E7E0EB</color>
    <color name="deep_purple_onSurfaceVariant">#49454E</color>
    <color name="deep_purple_outline">#7A757F</color>
    <color name="deep_purple_outlineVariant">#CBC4CF</color>
    <color name="deep_purple_inverseSurface">#322F35</color>
    <color name="deep_purple_inverseOnSurface">#F5EFF7</color>
    <color name="deep_purple_inversePrimary">#D3BCFD</color>
    <color name="deep_purple_primaryFixed">#EBDDFF</color>
    <color name="deep_purple_onPrimaryFixed">#230F46</color>
    <color name="deep_purple_primaryFixedDim">#D3BCFD</color>
    <color name="deep_purple_onPrimaryFixedVariant">#4F3D74</color>
    <color name="deep_purple_secondaryFixed">#E9DEF8</color>
    <color name="deep_purple_onSecondaryFixed">#1F182B</color>
    <color name="deep_purple_secondaryFixedDim">#CDC2DB</color>
    <color name="deep_purple_onSecondaryFixedVariant">#4B4358</color>
    <color name="deep_purple_tertiaryFixed">#FFD9E1</color>
    <color name="deep_purple_onTertiaryFixed">#31101B</color>
    <color name="deep_purple_tertiaryFixedDim">#F0B7C5</color>
    <color name="deep_purple_onTertiaryFixedVariant">#643B46</color>
    <color name="deep_purple_surfaceDim">#DED8E0</color>
    <color name="deep_purple_surfaceBright">#FEF7FF</color>
    <color name="deep_purple_surfaceContainerLowest">#FFFFFF</color>
    <color name="deep_purple_surfaceContainerLow">#F8F1FA</color>
    <color name="deep_purple_surfaceContainer">#F2ECF4</color>
    <color name="deep_purple_surfaceContainerHigh">#EDE6EE</color>
    <color name="deep_purple_surfaceContainerHighest">#E7E0E8</color>

    <color name="indigo_primary">#515B92</color>
    <color name="indigo_onPrimary">#FFFFFF</color>
    <color name="indigo_primaryContainer">#DEE0FF</color>
    <color name="indigo_onPrimaryContainer">#0B154B</color>
    <color name="indigo_secondary">#5B5D72</color>
    <color name="indigo_onSecondary">#FFFFFF</color>
    <color name="indigo_secondaryContainer">#E0E1F9</color>
    <color name="indigo_onSecondaryContainer">#181A2C</color>
    <color name="indigo_tertiary">#77536D</color>
    <color name="indigo_onTertiary">#FFFFFF</color>
    <color name="indigo_tertiaryContainer">#FFD7F1</color>
    <color name="indigo_onTertiaryContainer">#2D1228</color>
    <color name="indigo_error">#BA1A1A</color>
    <color name="indigo_onError">#FFFFFF</color>
    <color name="indigo_errorContainer">#FFDAD6</color>
    <color name="indigo_onErrorContainer">#410002</color>
    <color name="indigo_background">#FBF8FF</color>
    <color name="indigo_onBackground">#1B1B21</color>
    <color name="indigo_surface">#FBF8FF</color>
    <color name="indigo_onSurface">#1B1B21</color>
    <color name="indigo_surfaceVariant">#E3E1EC</color>
    <color name="indigo_onSurfaceVariant">#46464F</color>
    <color name="indigo_outline">#767680</color>
    <color name="indigo_outlineVariant">#C7C5D0</color>
    <color name="indigo_inverseSurface">#303036</color>
    <color name="indigo_inverseOnSurface">#F2EFF7</color>
    <color name="indigo_inversePrimary">#BAC3FF</color>
    <color name="indigo_primaryFixed">#DEE0FF</color>
    <color name="indigo_onPrimaryFixed">#0B154B</color>
    <color name="indigo_primaryFixedDim">#BAC3FF</color>
    <color name="indigo_onPrimaryFixedVariant">#394379</color>
    <color name="indigo_secondaryFixed">#E0E1F9</color>
    <color name="indigo_onSecondaryFixed">#181A2C</color>
    <color name="indigo_secondaryFixedDim">#C3C5DD</color>
    <color name="indigo_onSecondaryFixedVariant">#434659</color>
    <color name="indigo_tertiaryFixed">#FFD7F1</color>
    <color name="indigo_onTertiaryFixed">#2D1228</color>
    <color name="indigo_tertiaryFixedDim">#E6BAD7</color>
    <color name="indigo_onTertiaryFixedVariant">#5D3C55</color>
    <color name="indigo_surfaceDim">#DBD9E0</color>
    <color name="indigo_surfaceBright">#FBF8FF</color>
    <color name="indigo_surfaceContainerLowest">#FFFFFF</color>
    <color name="indigo_surfaceContainerLow">#F5F2FA</color>
    <color name="indigo_surfaceContainer">#EFEDF4</color>
    <color name="indigo_surfaceContainerHigh">#E9E7EF</color>
    <color name="indigo_surfaceContainerHighest">#E4E1E9</color>

    <color name="blue_primary">#37618E</color>
    <color name="blue_onPrimary">#FFFFFF</color>
    <color name="blue_primaryContainer">#D2E4FF</color>
    <color name="blue_onPrimaryContainer">#001C37</color>
    <color name="blue_secondary">#535F70</color>
    <color name="blue_onSecondary">#FFFFFF</color>
    <color name="blue_secondaryContainer">#D7E3F8</color>
    <color name="blue_onSecondaryContainer">#101C2B</color>
    <color name="blue_tertiary">#6B5778</color>
    <color name="blue_onTertiary">#FFFFFF</color>
    <color name="blue_tertiaryContainer">#F3DAFF</color>
    <color name="blue_onTertiaryContainer">#251431</color>
    <color name="blue_error">#BA1A1A</color>
    <color name="blue_onError">#FFFFFF</color>
    <color name="blue_errorContainer">#FFDAD6</color>
    <color name="blue_onErrorContainer">#410002</color>
    <color name="blue_background">#F8F9FF</color>
    <color name="blue_onBackground">#191C20</color>
    <color name="blue_surface">#F8F9FF</color>
    <color name="blue_onSurface">#191C20</color>
    <color name="blue_surfaceVariant">#DFE2EB</color>
    <color name="blue_onSurfaceVariant">#43474E</color>
    <color name="blue_outline">#73777F</color>
    <color name="blue_outlineVariant">#C3C6CF</color>
    <color name="blue_inverseSurface">#2E3135</color>
    <color name="blue_inverseOnSurface">#EFF0F7</color>
    <color name="blue_inversePrimary">#A1C9FD</color>
    <color name="blue_primaryFixed">#D2E4FF</color>
    <color name="blue_onPrimaryFixed">#001C37</color>
    <color name="blue_primaryFixedDim">#A1C9FD</color>
    <color name="blue_onPrimaryFixedVariant">#1B4975</color>
    <color name="blue_secondaryFixed">#D7E3F8</color>
    <color name="blue_onSecondaryFixed">#101C2B</color>
    <color name="blue_secondaryFixedDim">#BBC7DB</color>
    <color name="blue_onSecondaryFixedVariant">#3C4858</color>
    <color name="blue_tertiaryFixed">#F3DAFF</color>
    <color name="blue_onTertiaryFixed">#251431</color>
    <color name="blue_tertiaryFixedDim">#D7BDE4</color>
    <color name="blue_onTertiaryFixedVariant">#533F5F</color>
    <color name="blue_surfaceDim">#D8DAE0</color>
    <color name="blue_surfaceBright">#F8F9FF</color>
    <color name="blue_surfaceContainerLowest">#FFFFFF</color>
    <color name="blue_surfaceContainerLow">#F2F3FA</color>
    <color name="blue_surfaceContainer">#ECEEF4</color>
    <color name="blue_surfaceContainerHigh">#E7E8EE</color>
    <color name="blue_surfaceContainerHighest">#E1E2E8</color>

    <color name="deep_blue_primary">#136682</color>
    <color name="deep_blue_onPrimary">#FFFFFF</color>
    <color name="deep_blue_primaryContainer">#BEE9FF</color>
    <color name="deep_blue_onPrimaryContainer">#001F2A</color>
    <color name="deep_blue_secondary">#4D616C</color>
    <color name="deep_blue_onSecondary">#FFFFFF</color>
    <color name="deep_blue_secondaryContainer">#D0E6F2</color>
    <color name="deep_blue_onSecondaryContainer">#081E27</color>
    <color name="deep_blue_tertiary">#5D5B7D</color>
    <color name="deep_blue_onTertiary">#FFFFFF</color>
    <color name="deep_blue_tertiaryContainer">#E3DFFF</color>
    <color name="deep_blue_onTertiaryContainer">#1A1836</color>
    <color name="deep_blue_error">#BA1A1A</color>
    <color name="deep_blue_onError">#FFFFFF</color>
    <color name="deep_blue_errorContainer">#FFDAD6</color>
    <color name="deep_blue_onErrorContainer">#410002</color>
    <color name="deep_blue_background">#F6FAFE</color>
    <color name="deep_blue_onBackground">#171C1F</color>
    <color name="deep_blue_surface">#F6FAFE</color>
    <color name="deep_blue_onSurface">#171C1F</color>
    <color name="deep_blue_surfaceVariant">#DCE4E9</color>
    <color name="deep_blue_onSurfaceVariant">#40484C</color>
    <color name="deep_blue_outline">#70787D</color>
    <color name="deep_blue_outlineVariant">#C0C8CD</color>
    <color name="deep_blue_inverseSurface">#2C3134</color>
    <color name="deep_blue_inverseOnSurface">#EDF1F5</color>
    <color name="deep_blue_inversePrimary">#8BD0F0</color>
    <color name="deep_blue_primaryFixed">#BEE9FF</color>
    <color name="deep_blue_onPrimaryFixed">#001F2A</color>
    <color name="deep_blue_primaryFixedDim">#8BD0F0</color>
    <color name="deep_blue_onPrimaryFixedVariant">#004D64</color>
    <color name="deep_blue_secondaryFixed">#D0E6F2</color>
    <color name="deep_blue_onSecondaryFixed">#081E27</color>
    <color name="deep_blue_secondaryFixedDim">#B4CAD6</color>
    <color name="deep_blue_onSecondaryFixedVariant">#354A54</color>
    <color name="deep_blue_tertiaryFixed">#E3DFFF</color>
    <color name="deep_blue_onTertiaryFixed">#1A1836</color>
    <color name="deep_blue_tertiaryFixedDim">#C6C2EA</color>
    <color name="deep_blue_onTertiaryFixedVariant">#454364</color>
    <color name="deep_blue_surfaceDim">#D6DBDE</color>
    <color name="deep_blue_surfaceBright">#F6FAFE</color>
    <color name="deep_blue_surfaceContainerLowest">#FFFFFF</color>
    <color name="deep_blue_surfaceContainerLow">#F0F4F8</color>
    <color name="deep_blue_surfaceContainer">#EAEEF2</color>
    <color name="deep_blue_surfaceContainerHigh">#E4E9EC</color>
    <color name="deep_blue_surfaceContainerHighest">#DFE3E7</color>

    <color name="cyan_primary">#006877</color>
    <color name="cyan_onPrimary">#FFFFFF</color>
    <color name="cyan_primaryContainer">#A4EEFF</color>
    <color name="cyan_onPrimaryContainer">#001F25</color>
    <color name="cyan_secondary">#4B6268</color>
    <color name="cyan_onSecondary">#FFFFFF</color>
    <color name="cyan_secondaryContainer">#CDE7ED</color>
    <color name="cyan_onSecondaryContainer">#051F24</color>
    <color name="cyan_tertiary">#545D7E</color>
    <color name="cyan_onTertiary">#FFFFFF</color>
    <color name="cyan_tertiaryContainer">#DCE1FF</color>
    <color name="cyan_onTertiaryContainer">#111A37</color>
    <color name="cyan_error">#BA1A1A</color>
    <color name="cyan_onError">#FFFFFF</color>
    <color name="cyan_errorContainer">#FFDAD6</color>
    <color name="cyan_onErrorContainer">#410002</color>
    <color name="cyan_background">#F5FAFC</color>
    <color name="cyan_onBackground">#171D1E</color>
    <color name="cyan_surface">#F5FAFC</color>
    <color name="cyan_onSurface">#171D1E</color>
    <color name="cyan_surfaceVariant">#DBE4E7</color>
    <color name="cyan_onSurfaceVariant">#3F484B</color>
    <color name="cyan_outline">#6F797B</color>
    <color name="cyan_outlineVariant">#BFC8CB</color>
    <color name="cyan_inverseSurface">#2B3133</color>
    <color name="cyan_inverseOnSurface">#ECF2F3</color>
    <color name="cyan_inversePrimary">#83D2E4</color>
    <color name="cyan_primaryFixed">#A4EEFF</color>
    <color name="cyan_onPrimaryFixed">#001F25</color>
    <color name="cyan_primaryFixedDim">#83D2E4</color>
    <color name="cyan_onPrimaryFixedVariant">#004E5A</color>
    <color name="cyan_secondaryFixed">#CDE7ED</color>
    <color name="cyan_onSecondaryFixed">#051F24</color>
    <color name="cyan_secondaryFixedDim">#B2CBD1</color>
    <color name="cyan_onSecondaryFixedVariant">#334A50</color>
    <color name="cyan_tertiaryFixed">#DCE1FF</color>
    <color name="cyan_onTertiaryFixed">#111A37</color>
    <color name="cyan_tertiaryFixedDim">#BDC5EB</color>
    <color name="cyan_onTertiaryFixedVariant">#3D4565</color>
    <color name="cyan_surfaceDim">#D5DBDD</color>
    <color name="cyan_surfaceBright">#F5FAFC</color>
    <color name="cyan_surfaceContainerLowest">#FFFFFF</color>
    <color name="cyan_surfaceContainerLow">#EFF4F6</color>
    <color name="cyan_surfaceContainer">#E9EFF0</color>
    <color name="cyan_surfaceContainerHigh">#E3E9EB</color>
    <color name="cyan_surfaceContainerHighest">#DEE3E5</color>

    <color name="teal_primary">#006B5F</color>
    <color name="teal_onPrimary">#FFFFFF</color>
    <color name="teal_primaryContainer">#9EF2E3</color>
    <color name="teal_onPrimaryContainer">#00201C</color>
    <color name="teal_secondary">#4A635E</color>
    <color name="teal_onSecondary">#FFFFFF</color>
    <color name="teal_secondaryContainer">#CCE8E2</color>
    <color name="teal_onSecondaryContainer">#06201C</color>
    <color name="teal_tertiary">#456179</color>
    <color name="teal_onTertiary">#FFFFFF</color>
    <color name="teal_tertiaryContainer">#CCE5FF</color>
    <color name="teal_onTertiaryContainer">#001E31</color>
    <color name="teal_error">#BA1A1A</color>
    <color name="teal_onError">#FFFFFF</color>
    <color name="teal_errorContainer">#FFDAD6</color>
    <color name="teal_onErrorContainer">#410002</color>
    <color name="teal_background">#F4FBF8</color>
    <color name="teal_onBackground">#161D1B</color>
    <color name="teal_surface">#F4FBF8</color>
    <color name="teal_onSurface">#161D1B</color>
    <color name="teal_surfaceVariant">#DAE5E1</color>
    <color name="teal_onSurfaceVariant">#3F4946</color>
    <color name="teal_outline">#6F7976</color>
    <color name="teal_outlineVariant">#BEC9C5</color>
    <color name="teal_inverseSurface">#2B3230</color>
    <color name="teal_inverseOnSurface">#ECF2EF</color>
    <color name="teal_inversePrimary">#82D5C7</color>
    <color name="teal_primaryFixed">#9EF2E3</color>
    <color name="teal_onPrimaryFixed">#00201C</color>
    <color name="teal_primaryFixedDim">#82D5C7</color>
    <color name="teal_onPrimaryFixedVariant">#005048</color>
    <color name="teal_secondaryFixed">#CCE8E2</color>
    <color name="teal_onSecondaryFixed">#06201C</color>
    <color name="teal_secondaryFixedDim">#B1CCC6</color>
    <color name="teal_onSecondaryFixedVariant">#334B47</color>
    <color name="teal_tertiaryFixed">#CCE5FF</color>
    <color name="teal_onTertiaryFixed">#001E31</color>
    <color name="teal_tertiaryFixedDim">#ADCAE5</color>
    <color name="teal_onTertiaryFixedVariant">#2D4960</color>
    <color name="teal_surfaceDim">#D5DBD9</color>
    <color name="teal_surfaceBright">#F4FBF8</color>
    <color name="teal_surfaceContainerLowest">#FFFFFF</color>
    <color name="teal_surfaceContainerLow">#EFF5F2</color>
    <color name="teal_surfaceContainer">#E9EFEC</color>
    <color name="teal_surfaceContainerHigh">#E3EAE7</color>
    <color name="teal_surfaceContainerHighest">#DDE4E1</color>

    <color name="green_primary">#3C6838</color>
    <color name="green_onPrimary">#FFFFFF</color>
    <color name="green_primaryContainer">#BDF0B3</color>
    <color name="green_onPrimaryContainer">#002203</color>
    <color name="green_secondary">#53634E</color>
    <color name="green_onSecondary">#FFFFFF</color>
    <color name="green_secondaryContainer">#D6E8CE</color>
    <color name="green_onSecondaryContainer">#111F0F</color>
    <color name="green_tertiary">#38656A</color>
    <color name="green_onTertiary">#FFFFFF</color>
    <color name="green_tertiaryContainer">#BCEBF0</color>
    <color name="green_onTertiaryContainer">#002022</color>
    <color name="green_error">#BA1A1A</color>
    <color name="green_onError">#FFFFFF</color>
    <color name="green_errorContainer">#FFDAD6</color>
    <color name="green_onErrorContainer">#410002</color>
    <color name="green_background">#F7FBF1</color>
    <color name="green_onBackground">#191D17</color>
    <color name="green_surface">#F7FBF1</color>
    <color name="green_onSurface">#191D17</color>
    <color name="green_surfaceVariant">#DEE5D8</color>
    <color name="green_onSurfaceVariant">#424940</color>
    <color name="green_outline">#73796F</color>
    <color name="green_outlineVariant">#C2C8BD</color>
    <color name="green_inverseSurface">#2D322B</color>
    <color name="green_inverseOnSurface">#EFF2E9</color>
    <color name="green_inversePrimary">#A2D399</color>
    <color name="green_primaryFixed">#BDF0B3</color>
    <color name="green_onPrimaryFixed">#002203</color>
    <color name="green_primaryFixedDim">#A2D399</color>
    <color name="green_onPrimaryFixedVariant">#255023</color>
    <color name="green_secondaryFixed">#D6E8CE</color>
    <color name="green_onSecondaryFixed">#111F0F</color>
    <color name="green_secondaryFixedDim">#BACCB3</color>
    <color name="green_onSecondaryFixedVariant">#3B4B38</color>
    <color name="green_tertiaryFixed">#BCEBF0</color>
    <color name="green_onTertiaryFixed">#002022</color>
    <color name="green_tertiaryFixedDim">#A0CFD4</color>
    <color name="green_onTertiaryFixedVariant">#1E4D52</color>
    <color name="green_surfaceDim">#D8DBD2</color>
    <color name="green_surfaceBright">#F7FBF1</color>
    <color name="green_surfaceContainerLowest">#FFFFFF</color>
    <color name="green_surfaceContainerLow">#F2F5EB</color>
    <color name="green_surfaceContainer">#ECEFE6</color>
    <color name="green_surfaceContainerHigh">#E6E9E0</color>
    <color name="green_surfaceContainerHighest">#E0E4DA</color>

    <color name="deep_green_primary">#4A672D</color>
    <color name="deep_green_onPrimary">#FFFFFF</color>
    <color name="deep_green_primaryContainer">#CBEDA5</color>
    <color name="deep_green_onPrimaryContainer">#0E2000</color>
    <color name="deep_green_secondary">#57624A</color>
    <color name="deep_green_onSecondary">#FFFFFF</color>
    <color name="deep_green_secondaryContainer">#DBE7C8</color>
    <color name="deep_green_onSecondaryContainer">#151E0B</color>
    <color name="deep_green_tertiary">#386664</color>
    <color name="deep_green_onTertiary">#FFFFFF</color>
    <color name="deep_green_tertiaryContainer">#BBECE8</color>
    <color name="deep_green_onTertiaryContainer">#00201F</color>
    <color name="deep_green_error">#BA1A1A</color>
    <color name="deep_green_onError">#FFFFFF</color>
    <color name="deep_green_errorContainer">#FFDAD6</color>
    <color name="deep_green_onErrorContainer">#410002</color>
    <color name="deep_green_background">#F9FAEF</color>
    <color name="deep_green_onBackground">#1A1C16</color>
    <color name="deep_green_surface">#F9FAEF</color>
    <color name="deep_green_onSurface">#1A1C16</color>
    <color name="deep_green_surfaceVariant">#E1E4D5</color>
    <color name="deep_green_onSurfaceVariant">#44483D</color>
    <color name="deep_green_outline">#75796C</color>
    <color name="deep_green_outlineVariant">#C4C8BA</color>
    <color name="deep_green_inverseSurface">#2F312A</color>
    <color name="deep_green_inverseOnSurface">#F0F2E6</color>
    <color name="deep_green_inversePrimary">#B0D18B</color>
    <color name="deep_green_primaryFixed">#CBEDA5</color>
    <color name="deep_green_onPrimaryFixed">#0E2000</color>
    <color name="deep_green_primaryFixedDim">#B0D18B</color>
    <color name="deep_green_onPrimaryFixedVariant">#334E17</color>
    <color name="deep_green_secondaryFixed">#DBE7C8</color>
    <color name="deep_green_onSecondaryFixed">#151E0B</color>
    <color name="deep_green_secondaryFixedDim">#BFCBAD</color>
    <color name="deep_green_onSecondaryFixedVariant">#404A34</color>
    <color name="deep_green_tertiaryFixed">#BBECE8</color>
    <color name="deep_green_onTertiaryFixed">#00201F</color>
    <color name="deep_green_tertiaryFixedDim">#A0CFCC</color>
    <color name="deep_green_onTertiaryFixedVariant">#1F4E4C</color>
    <color name="deep_green_surfaceDim">#D9DBD0</color>
    <color name="deep_green_surfaceBright">#F9FAEF</color>
    <color name="deep_green_surfaceContainerLowest">#FFFFFF</color>
    <color name="deep_green_surfaceContainerLow">#F3F5E9</color>
    <color name="deep_green_surfaceContainer">#EDEFE4</color>
    <color name="deep_green_surfaceContainerHigh">#E8E9DE</color>
    <color name="deep_green_surfaceContainerHighest">#E2E3D8</color>

    <color name="lime_primary">#5B631E</color>
    <color name="lime_onPrimary">#FFFFFF</color>
    <color name="lime_primaryContainer">#E0E995</color>
    <color name="lime_onPrimaryContainer">#1A1E00</color>
    <color name="lime_secondary">#5E6144</color>
    <color name="lime_onSecondary">#FFFFFF</color>
    <color name="lime_secondaryContainer">#E3E5C1</color>
    <color name="lime_onSecondaryContainer">#1B1D07</color>
    <color name="lime_tertiary">#3B665B</color>
    <color name="lime_onTertiary">#FFFFFF</color>
    <color name="lime_tertiaryContainer">#BEECDD</color>
    <color name="lime_onTertiaryContainer">#002019</color>
    <color name="lime_error">#BA1A1A</color>
    <color name="lime_onError">#FFFFFF</color>
    <color name="lime_errorContainer">#FFDAD6</color>
    <color name="lime_onErrorContainer">#410002</color>
    <color name="lime_background">#FCFAED</color>
    <color name="lime_onBackground">#1B1C14</color>
    <color name="lime_surface">#FCFAED</color>
    <color name="lime_onSurface">#1B1C14</color>
    <color name="lime_surfaceVariant">#E4E3D2</color>
    <color name="lime_onSurfaceVariant">#47483B</color>
    <color name="lime_outline">#78786A</color>
    <color name="lime_outlineVariant">#C8C7B7</color>
    <color name="lime_inverseSurface">#313128</color>
    <color name="lime_inverseOnSurface">#F3F1E4</color>
    <color name="lime_inversePrimary">#C3CD7B</color>
    <color name="lime_primaryFixed">#E0E995</color>
    <color name="lime_onPrimaryFixed">#1A1E00</color>
    <color name="lime_primaryFixedDim">#C3CD7B</color>
    <color name="lime_onPrimaryFixedVariant">#434B05</color>
    <color name="lime_secondaryFixed">#E3E5C1</color>
    <color name="lime_onSecondaryFixed">#1B1D07</color>
    <color name="lime_secondaryFixedDim">#C7C9A7</color>
    <color name="lime_onSecondaryFixedVariant">#46492F</color>
    <color name="lime_tertiaryFixed">#BEECDD</color>
    <color name="lime_onTertiaryFixed">#002019</color>
    <color name="lime_tertiaryFixedDim">#A2D0C1</color>
    <color name="lime_onTertiaryFixedVariant">#224E43</color>
    <color name="lime_surfaceDim">#DCDACE</color>
    <color name="lime_surfaceBright">#FCFAED</color>
    <color name="lime_surfaceContainerLowest">#FFFFFF</color>
    <color name="lime_surfaceContainerLow">#F6F4E7</color>
    <color name="lime_surfaceContainer">#F0EEE1</color>
    <color name="lime_surfaceContainerHigh">#EAE9DC</color>
    <color name="lime_surfaceContainerHighest">#E5E3D6</color>

    <color name="yellow_primary">#7D570D</color>
    <color name="yellow_onPrimary">#FFFFFF</color>
    <color name="yellow_primaryContainer">#FFDEAC</color>
    <color name="yellow_onPrimaryContainer">#281900</color>
    <color name="yellow_secondary">#6E5C40</color>
    <color name="yellow_onSecondary">#FFFFFF</color>
    <color name="yellow_secondaryContainer">#F8DFBB</color>
    <color name="yellow_onSecondaryContainer">#261904</color>
    <color name="yellow_tertiary">#4E6542</color>
    <color name="yellow_onTertiary">#FFFFFF</color>
    <color name="yellow_tertiaryContainer">#D1EABF</color>
    <color name="yellow_onTertiaryContainer">#0D2005</color>
    <color name="yellow_error">#BA1A1A</color>
    <color name="yellow_onError">#FFFFFF</color>
    <color name="yellow_errorContainer">#FFDAD6</color>
    <color name="yellow_onErrorContainer">#410002</color>
    <color name="yellow_background">#FFF8F3</color>
    <color name="yellow_onBackground">#201B13</color>
    <color name="yellow_surface">#FFF8F3</color>
    <color name="yellow_onSurface">#201B13</color>
    <color name="yellow_surfaceVariant">#EFE0CF</color>
    <color name="yellow_onSurfaceVariant">#4E4539</color>
    <color name="yellow_outline">#807567</color>
    <color name="yellow_outlineVariant">#D2C4B4</color>
    <color name="yellow_inverseSurface">#362F27</color>
    <color name="yellow_inverseOnSurface">#FBEFE2</color>
    <color name="yellow_inversePrimary">#F0BE6D</color>
    <color name="yellow_primaryFixed">#FFDEAC</color>
    <color name="yellow_onPrimaryFixed">#281900</color>
    <color name="yellow_primaryFixedDim">#F0BE6D</color>
    <color name="yellow_onPrimaryFixedVariant">#604100</color>
    <color name="yellow_secondaryFixed">#F8DFBB</color>
    <color name="yellow_onSecondaryFixed">#261904</color>
    <color name="yellow_secondaryFixedDim">#DBC3A1</color>
    <color name="yellow_onSecondaryFixedVariant">#55442A</color>
    <color name="yellow_tertiaryFixed">#D1EABF</color>
    <color name="yellow_onTertiaryFixed">#0D2005</color>
    <color name="yellow_tertiaryFixedDim">#B5CEA4</color>
    <color name="yellow_onTertiaryFixedVariant">#374C2C</color>
    <color name="yellow_surfaceDim">#E4D8CC</color>
    <color name="yellow_surfaceBright">#FFF8F3</color>
    <color name="yellow_surfaceContainerLowest">#FFFFFF</color>
    <color name="yellow_surfaceContainerLow">#FEF2E5</color>
    <color name="yellow_surfaceContainer">#F8ECDF</color>
    <color name="yellow_surfaceContainerHigh">#F2E6D9</color>
    <color name="yellow_surfaceContainerHighest">#ECE1D4</color>

    <color name="orange_primary">#88511D</color>
    <color name="orange_onPrimary">#FFFFFF</color>
    <color name="orange_primaryContainer">#FFDCC2</color>
    <color name="orange_onPrimaryContainer">#2E1500</color>
    <color name="orange_secondary">#745944</color>
    <color name="orange_onSecondary">#FFFFFF</color>
    <color name="orange_secondaryContainer">#FFDCC2</color>
    <color name="orange_onSecondaryContainer">#2A1707</color>
    <color name="orange_tertiary">#5B6237</color>
    <color name="orange_onTertiary">#FFFFFF</color>
    <color name="orange_tertiaryContainer">#E0E7B1</color>
    <color name="orange_onTertiaryContainer">#191E00</color>
    <color name="orange_error">#BA1A1A</color>
    <color name="orange_onError">#FFFFFF</color>
    <color name="orange_errorContainer">#FFDAD6</color>
    <color name="orange_onErrorContainer">#410002</color>
    <color name="orange_background">#FFF8F5</color>
    <color name="orange_onBackground">#221A14</color>
    <color name="orange_surface">#FFF8F5</color>
    <color name="orange_onSurface">#221A14</color>
    <color name="orange_surfaceVariant">#F3DFD1</color>
    <color name="orange_onSurfaceVariant">#51443B</color>
    <color name="orange_outline">#847469</color>
    <color name="orange_outlineVariant">#D6C3B6</color>
    <color name="orange_inverseSurface">#382F28</color>
    <color name="orange_inverseOnSurface">#FEEEE4</color>
    <color name="orange_inversePrimary">#FFB77B</color>
    <color name="orange_primaryFixed">#FFDCC2</color>
    <color name="orange_onPrimaryFixed">#2E1500</color>
    <color name="orange_primaryFixedDim">#FFB77B</color>
    <color name="orange_onPrimaryFixedVariant">#6B3A05</color>
    <color name="orange_secondaryFixed">#FFDCC2</color>
    <color name="orange_onSecondaryFixed">#2A1707</color>
    <color name="orange_secondaryFixedDim">#E3C0A5</color>
    <color name="orange_onSecondaryFixedVariant">#5A422E</color>
    <color name="orange_tertiaryFixed">#E0E7B1</color>
    <color name="orange_onTertiaryFixed">#191E00</color>
    <color name="orange_tertiaryFixedDim">#C4CB97</color>
    <color name="orange_onTertiaryFixedVariant">#444A22</color>
    <color name="orange_surfaceDim">#E7D7CD</color>
    <color name="orange_surfaceBright">#FFF8F5</color>
    <color name="orange_surfaceContainerLowest">#FFFFFF</color>
    <color name="orange_surfaceContainerLow">#FFF1E8</color>
    <color name="orange_surfaceContainer">#FBEBE1</color>
    <color name="orange_surfaceContainerHigh">#F5E5DB</color>
    <color name="orange_surfaceContainerHighest">#EFE0D6</color>

    <color name="brown_primary">#604238</color>
    <color name="brown_onPrimary">#FFFFFF</color>
    <color name="brown_primaryContainer">#87665A</color>
    <color name="brown_onPrimaryContainer">#FFFFFF</color>
    <color name="brown_secondary">#6B5B55</color>
    <color name="brown_onSecondary">#FFFFFF</color>
    <color name="brown_secondaryContainer">#F9E2DB</color>
    <color name="brown_onSecondaryContainer">#574742</color>
    <color name="brown_tertiary">#4F4A2D</color>
    <color name="brown_onTertiary">#FFFFFF</color>
    <color name="brown_tertiaryContainer">#746E4E</color>
    <color name="brown_onTertiaryContainer">#FFFFFF</color>
    <color name="brown_error">#BA1A1A</color>
    <color name="brown_onError">#FFFFFF</color>
    <color name="brown_errorContainer">#FFDAD6</color>
    <color name="brown_onErrorContainer">#410002</color>
    <color name="brown_background">#FFF8F6</color>
    <color name="brown_onBackground">#1E1B1A</color>
    <color name="brown_surface">#FFF8F6</color>
    <color name="brown_onSurface">#1E1B1A</color>
    <color name="brown_surfaceVariant">#F1DFD9</color>
    <color name="brown_onSurfaceVariant">#504440</color>
    <color name="brown_outline">#827470</color>
    <color name="brown_outlineVariant">#D4C3BE</color>
    <color name="brown_inverseSurface">#33302E</color>
    <color name="brown_inverseOnSurface">#F7EFED</color>
    <color name="brown_inversePrimary">#E7BDB0</color>
    <color name="brown_primaryFixed">#FFDBCF</color>
    <color name="brown_onPrimaryFixed">#2C160D</color>
    <color name="brown_primaryFixedDim">#E7BDB0</color>
    <color name="brown_onPrimaryFixedVariant">#5D4036</color>
    <color name="brown_secondaryFixed">#F5DED6</color>
    <color name="brown_onSecondaryFixed">#251915</color>
    <color name="brown_secondaryFixedDim">#D8C2BB</color>
    <color name="brown_onSecondaryFixedVariant">#53433E</color>
    <color name="brown_tertiaryFixed">#ECE3BB</color>
    <color name="brown_onTertiaryFixed">#201C04</color>
    <color name="brown_tertiaryFixedDim">#CFC7A1</color>
    <color name="brown_onTertiaryFixedVariant">#4C472A</color>
    <color name="brown_surfaceDim">#E0D8D6</color>
    <color name="brown_surfaceBright">#FFF8F6</color>
    <color name="brown_surfaceContainerLowest">#FFFFFF</color>
    <color name="brown_surfaceContainerLow">#FAF2F0</color>
    <color name="brown_surfaceContainer">#F4ECEA</color>
    <color name="brown_surfaceContainerHigh">#EFE6E4</color>
    <color name="brown_surfaceContainerHighest">#E9E1DF</color>

    <color name="grey_primary">#505151</color>
    <color name="grey_onPrimary">#FFFFFF</color>
    <color name="grey_primaryContainer">#757575</color>
    <color name="grey_onPrimaryContainer">#FFFFFF</color>
    <color name="grey_secondary">#5F5E5E</color>
    <color name="grey_onSecondary">#FFFFFF</color>
    <color name="grey_secondaryContainer">#E6E3E2</color>
    <color name="grey_onSecondaryContainer">#494848</color>
    <color name="grey_tertiary">#535052</color>
    <color name="grey_onTertiary">#FFFFFF</color>
    <color name="grey_tertiaryContainer">#787476</color>
    <color name="grey_onTertiaryContainer">#FFFFFF</color>
    <color name="grey_error">#BA1A1A</color>
    <color name="grey_onError">#FFFFFF</color>
    <color name="grey_errorContainer">#FFDAD6</color>
    <color name="grey_onErrorContainer">#410002</color>
    <color name="grey_background">#FCF8F8</color>
    <color name="grey_onBackground">#1C1B1B</color>
    <color name="grey_surface">#FCF8F8</color>
    <color name="grey_onSurface">#1C1B1B</color>
    <color name="grey_surfaceVariant">#E0E3E3</color>
    <color name="grey_onSurfaceVariant">#444748</color>
    <color name="grey_outline">#747878</color>
    <color name="grey_outlineVariant">#C4C7C7</color>
    <color name="grey_inverseSurface">#313030</color>
    <color name="grey_inverseOnSurface">#F4F0EF</color>
    <color name="grey_inversePrimary">#C7C6C6</color>
    <color name="grey_primaryFixed">#E3E2E2</color>
    <color name="grey_onPrimaryFixed">#1B1C1C</color>
    <color name="grey_primaryFixedDim">#C7C6C6</color>
    <color name="grey_onPrimaryFixedVariant">#464747</color>
    <color name="grey_secondaryFixed">#E5E2E1</color>
    <color name="grey_onSecondaryFixed">#1B1C1C</color>
    <color name="grey_secondaryFixedDim">#C8C6C5</color>
    <color name="grey_onSecondaryFixedVariant">#474746</color>
    <color name="grey_tertiaryFixed">#E7E1E3</color>
    <color name="grey_onTertiaryFixed">#1D1B1D</color>
    <color name="grey_tertiaryFixedDim">#CBC5C7</color>
    <color name="grey_onTertiaryFixedVariant">#494648</color>
    <color name="grey_surfaceDim">#DDD9D8</color>
    <color name="grey_surfaceBright">#FCF8F8</color>
    <color name="grey_surfaceContainerLowest">#FFFFFF</color>
    <color name="grey_surfaceContainerLow">#F7F3F2</color>
    <color name="grey_surfaceContainer">#F1EDEC</color>
    <color name="grey_surfaceContainerHigh">#EBE7E7</color>
    <color name="grey_surfaceContainerHighest">#E5E2E1</color>
</resources>