<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2017 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:keepScreenOn="true">

  <androidx.media3.ui.PlayerView android:id="@+id/player_view"
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1"
      android:background="@android:color/black"
      app:repeat_toggle_modes="all|one"/>

  <RelativeLayout android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1">

    <androidx.recyclerview.widget.RecyclerView android:id="@+id/sample_list"
        android:choiceMode="singleChoice"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical"
        android:fadeScrollbars="false"/>

    <com.google.android.material.floatingactionbutton.FloatingActionButton android:id="@+id/add_sample_button"
        android:src="@drawable/ic_plus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_margin="16dp"
        android:contentDescription="@string/add_samples"/>

  </RelativeLayout>

</LinearLayout>
