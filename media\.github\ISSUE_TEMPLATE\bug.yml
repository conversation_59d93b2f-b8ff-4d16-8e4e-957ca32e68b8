name: Bug Report
description: Report a bug in the Media3 library
labels: ["bug", "needs triage"]
body:
  - type: markdown
    attributes:
      value: |
        We can only process bug reports that are actionable. Unclear bug reports or reports with insufficient information may not get attention.

        Before filing a bug:
        -------------------------

        - Search existing issues, including issues that are closed: https://github.com/androidx/media/issues?q=is%3Aissue
        - For ExoPlayer-related bugs, please also check for existing issues on the ExoPlayer tracker: https://github.com/google/ExoPlayer/issues?q=is%3Aissue
  - type: dropdown
    attributes:
      label: Version
      description: What version of Media3 (or ExoPlayer) are you using?
      options:
        - Media3 main branch
        - Media3 pre-release (alpha, beta or RC not in this list)
        - Media3 1.5.0
        - Media3 1.4.1
        - Media3 1.4.0
        - Media3 1.3.1
        - Media3 1.3.0
        - Media3 1.2.1
        - Media3 1.2.0
        - Media3 1.1.1 / ExoPlayer 2.19.1
        - Media3 1.1.0 / ExoPlayer 2.19.0
        - Media3 1.0.2 / ExoPlayer 2.18.7
        - Media3 1.0.1 / ExoPlayer 2.18.6
        - Media3 1.0.0 / ExoPlayer 2.18.5
        - ExoPlayer 2.18.4
        - ExoPlayer 2.18.3
        - ExoPlayer 2.18.2
        - ExoPlayer 2.18.1
        - ExoPlayer 2.18.0
        - ExoPlayer 2.17.1
        - ExoPlayer 2.17.0
        - ExoPlayer 2.16.1
        - ExoPlayer 2.16.0
        - ExoPlayer 2.15.1
        - ExoPlayer 2.15.0
        - ExoPlayer 2.14.2
        - ExoPlayer 2.14.1
        - ExoPlayer 2.14.0
        - ExoPlayer dev-v2 branch
        - Older (unsupported)
    validations:
      required: true
  - type: textarea
    attributes:
      label: More version details
      description: >
        Required if you selected `main` or `dev-v2` (please provide an exact commit SHA),
        or 'pre-release' or 'older' (please provide the version).
  - type: textarea
    attributes:
      label: Devices that reproduce the issue
      placeholder: |
        Example:
        * Pixel 4 running Android 12
        * Samsung S21 running Android 11
    validations:
      required: true
  - type: textarea
    attributes:
      label: Devices that do not reproduce the issue
      placeholder: |
        Example:
        * Pixel 3 running Android Pie
  - type: dropdown
    attributes:
      label: Reproducible in the demo app?
      description: Please try and reproduce the issue in the [Media3 demo app](https://github.com/androidx/media/tree/release/demos/main).
      options:
        - "Yes"
        - "No"
        - Not tested
    validations:
      required: true
  - type: textarea
    attributes:
      label: Reproduction steps
      description: Clear and complete steps we can use to reproduce the problem
      placeholder: |
        Example:
        1. Play the attached media in the demo app
        2. Seek forward 10s
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected result
      placeholder: |
        Example:
        The media plays successfully
    validations:
      required: true
  - type: textarea
    attributes:
      label: Actual result
      placeholder: |
        Example:
        Playback crashes with the following stack trace:
        ...
    validations:
      required: true
  - type: textarea
    attributes:
      label: Media
      description: |
        Media we can use to reproduce the problem. Either:
        * Attach a file here
        * Include a media URL
        * Refer to a piece of media from the demo app (e.g. `Misc > Dizzy (MP4)`)
        * If you don't want to post media publicly please email the <NAME_EMAIL> with subject 'Issue #\<issuenumber\>' after filing this issue, and note that you will do this here.
        * If you are certain the issue does not depend on the media being played, enter "Not applicable" here.

        For DRM-protected media please also include the scheme and license server URL.
    validations:
      required: true
  - type: checkboxes
    attributes:
      label: Bug Report
      description: |
        After filing this issue please run `adb bugreport` shortly after reproducing the problem (ideally in the [demo app](https://github.com/androidx/media/tree/release/demos/main)) to capture a zip file, and email <NAME_EMAIL> with subject 'Issue #\<issuenumber\>'.

        **Note:** Logcat output is **not** the same as a full bug report, and is often missing information that's useful for diagnosing issues. Please ensure you're sending a full bug report zip file.
      options:
        - label: You will email the zip file produced by `adb bugreport` to <EMAIL> after filing this issue.
