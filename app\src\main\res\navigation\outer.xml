<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/absurd"
    app:startDestination="@id/main_fragment">

    <fragment
        android:id="@+id/main_fragment"
        android:name="org.oxycblt.auxio.MainFragment"
        android:label="main_fragment"
        tools:layout="@layout/fragment_main">
        <action
            android:id="@+id/preferences"
            app:destination="@id/root_preferences_fragment" />
        <action
            android:id="@+id/about"
            app:destination="@id/about_fragment" />
    </fragment>
    <fragment
        android:id="@+id/root_preferences_fragment"
        android:name="org.oxycblt.auxio.settings.RootPreferenceFragment"
        android:label="settings_fragment">
        <action
            android:id="@+id/ui_preferences"
            app:destination="@id/ui_preferences_fragment" />
        <action
            android:id="@+id/personalize_preferences"
            app:destination="@id/personalize_preferences_fragment" />
        <action
            android:id="@+id/music_preferences"
            app:destination="@id/music_preferences_fragment" />
        <action
            android:id="@+id/audio_peferences"
            app:destination="@id/audio_preferences_fragment" />
        <action
            android:id="@+id/music_locations_settings"
            app:destination="@id/music_locations_dialog" />
        <action
            android:id="@+id/excluded_locations_settings"
            app:destination="@id/excluded_locations_dialog" />
    </fragment>

    <fragment
        android:id="@+id/ui_preferences_fragment"
        android:name="org.oxycblt.auxio.settings.categories.UIPreferenceFragment"
        android:label="ui_preferences_fragment">
        <action
            android:id="@+id/accent_settings"
            app:destination="@id/accent_dialog" />
    </fragment>

    <fragment
        android:id="@+id/personalize_preferences_fragment"
        android:name="org.oxycblt.auxio.settings.categories.PersonalizePreferenceFragment"
        android:label="personalize_preferences_fragment">
        <action
            android:id="@+id/tab_settings"
            app:destination="@id/tab_dialog" />
    </fragment>

    <fragment
        android:id="@+id/music_preferences_fragment"
        android:name="org.oxycblt.auxio.settings.categories.MusicPreferenceFragment"
        android:label="personalize_preferences_fragment">
        <action
            android:id="@+id/separators_settings"
            app:destination="@id/separators_dialog" />
    </fragment>

    <fragment
        android:id="@+id/audio_preferences_fragment"
        android:name="org.oxycblt.auxio.settings.categories.AudioPreferenceFragment"
        android:label="personalize_preferences_fragment">
        <action
            android:id="@+id/pre_amp_settings"
            app:destination="@id/pre_amp_dialog" />
    </fragment>

    <dialog
        android:id="@+id/accent_dialog"
        android:name="org.oxycblt.auxio.ui.accent.AccentCustomizeDialog"
        android:label="accent_dialog"
        tools:layout="@layout/dialog_accent" />
    <dialog
        android:id="@+id/tab_dialog"
        android:name="org.oxycblt.auxio.home.tabs.TabCustomizeDialog"
        android:label="tab_dialog"
        tools:layout="@layout/dialog_tabs" />
    <dialog
        android:id="@+id/pre_amp_dialog"
        android:name="org.oxycblt.auxio.playback.replaygain.PreAmpCustomizeDialog"
        android:label="pre_amp_dialog"
        tools:layout="@layout/dialog_pre_amp" />
    <dialog
        android:id="@+id/music_locations_dialog"
        android:name="org.oxycblt.auxio.music.locations.MusicLocationsDialog"
        android:label="music_locations_dialog"
        tools:layout="@layout/dialog_music_locations" />
    <dialog
        android:id="@+id/separators_dialog"
        android:name="org.oxycblt.auxio.music.interpret.SeparatorsDialog"
        android:label="separators_dialog"
        tools:layout="@layout/dialog_separators" />
    <dialog
        android:id="@+id/excluded_locations_dialog"
        android:name="org.oxycblt.auxio.music.locations.ExcludedLocationsDialog"
        android:label="excluded_locations_dialog"
        tools:layout="@layout/dialog_music_locations" />

    <fragment
        android:id="@+id/about_fragment"
        android:name="org.oxycblt.auxio.settings.AboutFragment"
        android:label="dialog_about"
        tools:layout="@layout/fragment_about" />
</navigation>