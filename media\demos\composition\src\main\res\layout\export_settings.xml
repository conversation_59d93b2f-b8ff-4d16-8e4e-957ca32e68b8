<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2024 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  android:id="@+id/export_settings_list"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:orientation="vertical"
  android:padding="8dp">
  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:layout_marginBottom="12dp"
    android:layout_marginTop="12dp">
    <TextView
      android:layout_height="wrap_content"
      android:layout_width="0dp"
      android:layout_weight="1"
      android:text="@string/output_audio_mime_type"/>
    <Spinner
      android:id="@+id/audio_mime_spinner"
      android:layout_gravity="end|center_vertical"
      android:gravity="end"
      android:layout_height="wrap_content"
      android:layout_width="wrap_content"/>
  </LinearLayout>
  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:layout_marginBottom="12dp">
    <TextView
      android:layout_height="wrap_content"
      android:layout_width="0dp"
      android:layout_weight="1"
      android:text="@string/output_video_mime_type"/>
    <Spinner
      android:id="@+id/video_mime_spinner"
      android:layout_gravity="end|center_vertical"
      android:gravity="end"
      android:layout_height="wrap_content"
      android:layout_width="wrap_content"/>
  </LinearLayout>
  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical">
    <TextView
      android:layout_height="wrap_content"
      android:layout_width="0dp"
      android:layout_weight="1"
      android:text="@string/enable_debug_tracing"/>
    <CheckBox
      android:id="@+id/enable_debug_tracing_checkbox"
      android:layout_gravity="end"
      android:checked="false"
      android:layout_height="wrap_content"
      android:layout_width="wrap_content"/>
  </LinearLayout>
  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical">
    <TextView
      android:text="@string/use_media3_muxer"
      android:layout_height="wrap_content"
      android:layout_width="0dp"
      android:layout_weight="1" />
    <CheckBox
      android:id="@+id/use_media3_muxer_checkbox"
      android:layout_gravity="end"
      android:checked="false"
      android:layout_height="wrap_content"
      android:layout_width="wrap_content"/>
  </LinearLayout>
  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical">
    <TextView
      android:text="@string/produce_fragmented_mp4"
      android:layout_height="wrap_content"
      android:layout_width="0dp"
      android:layout_weight="1" />
    <CheckBox
      android:id="@+id/produce_fragmented_mp4_checkbox"
      android:layout_gravity="end"
      android:checked="false"
      android:layout_height="wrap_content"
      android:layout_width="wrap_content"/>
  </LinearLayout>
</LinearLayout>
