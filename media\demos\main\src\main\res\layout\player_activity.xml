<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:keepScreenOn="true">

  <androidx.media3.ui.PlayerView android:id="@+id/player_view"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      app:show_shuffle_button="true"
      app:show_subtitle_button="true"/>

  <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:background="#88000000"
      android:orientation="vertical">

    <TextView android:id="@+id/debug_text_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="4dp"
        android:paddingRight="4dp"
        android:textSize="10sp"
        tools:ignore="SmallSp"/>

    <LinearLayout android:id="@+id/controls_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="gone">

      <Button android:id="@+id/select_tracks_button"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:text="@string/track_selection_title"
          android:enabled="false"/>

    </LinearLayout>

  </LinearLayout>

</FrameLayout>
