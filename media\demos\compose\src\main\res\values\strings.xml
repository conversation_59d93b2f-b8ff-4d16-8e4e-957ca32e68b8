<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2024 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          https://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources>
  <string name="app_name">Media3 Compose Demo</string>
  <string name="current_playlist_name">Current playlist</string>
  <string name="open_player_content_description">Click to view your play list</string>
  <string name="added_media_item_format">Added %1$s to playlist</string>
  <string name="shuffle">Shuffle</string>
  <string name="play_button">Play</string>
  <string name="waiting_for_metadata">Waiting for playlist to load…</string>
  <string name="notification_permission_denied">
    "Without notification access the app can't warn about failed background operations"</string>
</resources>
