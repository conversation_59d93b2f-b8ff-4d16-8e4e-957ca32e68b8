<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

  <string name="application_name">ExoPlayer</string>

  <string name="track_selection_title">Select tracks</string>

  <string name="unexpected_intent_action">Unexpected intent action: <xliff:g id="action">%1$s</xliff:g></string>

  <string name="error_cleartext_not_permitted">Cleartext HTTP traffic not permitted. See https://developer.android.com/guide/topics/media/issues/cleartext-not-permitted</string>

  <string name="error_generic">Playback failed</string>

  <string name="error_drm_unsupported_scheme">This device does not support the required DRM scheme</string>

  <string name="error_no_decoder">This device does not provide a decoder for <xliff:g id="mime_type">%1$s</xliff:g></string>

  <string name="error_no_secure_decoder">This device does not provide a secure decoder for <xliff:g id="mime_type">%1$s</xliff:g></string>

  <string name="error_querying_decoders">Unable to query device decoders</string>

  <string name="error_instantiating_decoder">Unable to instantiate decoder <xliff:g id="decoder_name">%1$s</xliff:g></string>

  <string name="error_unsupported_video">Media includes video tracks, but none are playable by this device</string>

  <string name="error_unsupported_audio">Media includes audio tracks, but none are playable by this device</string>

  <string name="storage_permission_denied">Permission to access storage was denied</string>

  <string name="sample_list_load_error">One or more sample lists failed to load</string>

  <string name="post_notification_not_granted">Notifications suppressed. Grant permission to see download notifications.</string>

  <string name="download_start_error">Failed to start download</string>

  <string name="download_start_error_offline_license">Failed to obtain offline license</string>

  <string name="download_playlist_unsupported">This demo app does not support downloading playlists</string>

  <string name="download_scheme_unsupported">This demo app only supports downloading http streams</string>

  <string name="download_live_unsupported">This demo app does not support downloading live content</string>

  <string name="download_ads_unsupported">IMA does not support offline ads</string>

  <string name="download_only_widevine_drm_supported">This demo app only supports downloading unencrypted or Widevine DRM content</string>

  <string name="prefer_extension_decoders">Prefer extension decoders</string>

  <string name="track_selection_title_video">Video</string>

  <string name="track_selection_title_audio">Audio</string>

  <string name="track_selection_title_text">Text</string>

  <string name="track_selection_title_image">Image</string>

</resources>
