Auxio 是一款本地音乐播放器，它拥有快速、可靠的 UI/UX，没有其他音乐播放器中的诸多无用功能。和其他使用原生 MediaPlayer API 的应用相比，Auxio 基于现代媒体播放库进行构建，聆听体验更佳，有更好的音乐库支持，<b>正是一款音乐播放器应有的样子</b>。

<b>功能特性</b>

- 基于 Media3 ExoPlayer 的播放
- 源于最新 Material You 设计规范的灵动界面
- 优先考虑易用性的独到用户体验
- 可定制的播放器行为
- 支持唱片编号、多名艺术家、发行类型、精确/原始日期、排序标签以及更多
- 统一“艺术家”和“专辑艺术家”的高级“艺术家”系统
- 文件夹管理功能可以感知到 SD 卡
- 可靠的播放列表功能
- 播放状态持久化
- 支持 Android auto
- 自动无缝回放
- 完整的回放增益支持（包括 MP3、FLAC、OGG、OPUS 和 MP4 文件）
- 支持外部均衡器（如 Wavelet 这样的应用）
- 边到边设计
- 内嵌封面支持
- 检索功能
- 耳机连接时自动播放
- 按桌面尺寸自适应的风格化微件
- 完全离线且私密
- 没有圆角的专辑封面（即使你想要）
