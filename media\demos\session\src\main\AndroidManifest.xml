<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2021 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="androidx.media3.demo.session">

  <uses-sdk/>
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />

  <application
      android:allowBackup="false"
      android:icon="@mipmap/ic_launcher"
      android:label="@string/app_name"
      android:theme="@style/Theme.Media3Demo">

    <!-- Declare that this session demo supports Android Auto. -->
    <meta-data
      android:name="com.google.android.gms.car.application"
      android:resource="@xml/auto_app_desc" />

    <activity
        android:name=".MainActivity"
        android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>

    <activity
        android:name=".PlayerActivity"
        android:exported="true"
        android:launchMode="singleTop"
        android:theme="@style/Theme.AppCompat.NoActionBar"/>

    <activity
        android:name=".PlayableFolderActivity"
        android:exported="true"/>

    <service
        android:name=".PlaybackService"
        android:foregroundServiceType="mediaPlayback"
        android:exported="true">
      <intent-filter>
        <action android:name="androidx.media3.session.MediaLibraryService"/>
        <action android:name="android.media.browse.MediaBrowserService"/>
        <action android:name="android.media.action.MEDIA_PLAY_FROM_SEARCH"/>
      </intent-filter>
    </service>

  </application>

</manifest>
