---
name: Feature request
about: Issue template for a feature request.
title: ''
labels: enhancement, needs triage
assignees: ''
---

Before filing a feature request:
-----------------------
- Search existing open issues, specifically with the label ‘enhancement’:
  https://github.com/androidx/media/labels/enhancement
- For ExoPlayer-related feature requests, please also check for existing feature
  requests on the ExoPlayer tracker:
  https://github.com/google/ExoPlayer/labels/enhancement
- Search existing pull requests:
    - On this tracker: https://github.com/androidx/media/pulls,
    - On the ExoPlayer tracker: https://github.com/google/ExoPlayer/pulls

When filing a feature request:
-----------------------
Replace the content in the sections below.

### [REQUIRED] Use case description
Describe the use case or problem you are trying to solve in detail. If there are
any standards or specifications involved, please provide the relevant details.

### Proposed solution
A clear and concise description of your proposed solution, if you have one.

### Alternatives considered
A clear and concise description of any alternative solutions you considered,
if applicable.
